name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          frontend/package-lock.json
          browser-viewer/package-lock.json
    
    - name: 设置 Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
    
    # 前端代码质量检查
    - name: 安装前端依赖
      working-directory: frontend
      run: npm ci
      
    - name: 前端 ESLint 检查
      working-directory: frontend
      run: npm run lint
      
    - name: 前端类型检查
      working-directory: frontend
      run: npm run type-check
    
    # 浏览器插件代码质量检查
    - name: 安装插件依赖
      working-directory: browser-viewer
      run: npm ci
      
    - name: 插件 ESLint 检查
      working-directory: browser-viewer
      run: npm run lint
      
    - name: 插件类型检查
      working-directory: browser-viewer
      run: npm run type-check
    
    # 后端代码质量检查
    - name: 安装后端依赖
      working-directory: backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: 后端代码格式检查
      working-directory: backend
      run: |
        black --check .
        isort --check-only .
        
    - name: 后端代码质量检查
      working-directory: backend
      run: |
        flake8 .
        mypy app/

  # 后端测试
  backend-tests:
    name: 后端测试
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_digital_lazy
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
    
    - name: 安装依赖
      working-directory: backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: 运行测试
      working-directory: backend
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_digital_lazy
        REDIS_URL: redis://localhost:6379/1
        TESTING: true
      run: |
        pytest --cov=app --cov-report=xml --cov-report=html --junitxml=tests/reports/junit.xml
    
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: backend/tests/coverage/coverage.xml
        flags: backend
        name: backend-coverage
        
    - name: 上传测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: backend-test-reports
        path: |
          backend/tests/reports/
          backend/tests/coverage/

  # 前端测试
  frontend-tests:
    name: 前端测试
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: 安装依赖
      working-directory: frontend
      run: npm ci
    
    - name: 运行单元测试
      working-directory: frontend
      run: npm run test:coverage
    
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: frontend/tests/coverage/lcov.info
        flags: frontend
        name: frontend-coverage
        
    - name: 上传测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: frontend-test-reports
        path: |
          frontend/tests/reports/
          frontend/tests/coverage/

  # 浏览器插件测试
  browser-extension-tests:
    name: 浏览器插件测试
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: browser-viewer/package-lock.json
    
    - name: 安装依赖
      working-directory: browser-viewer
      run: npm ci
    
    - name: 运行测试
      working-directory: browser-viewer
      run: npm run test -- --coverage
        
    - name: 上传测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: extension-test-reports
        path: browser-viewer/tests/coverage/

  # E2E 测试
  e2e-tests:
    name: E2E 测试
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: 设置 Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
    
    - name: 安装 Playwright
      working-directory: frontend
      run: |
        npm ci
        npx playwright install --with-deps
    
    - name: 启动后端服务
      working-directory: backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        python simple_main.py &
        sleep 10
    
    - name: 启动前端服务
      working-directory: frontend
      run: |
        npm run build
        npm run preview &
        sleep 10
    
    - name: 运行 E2E 测试
      working-directory: frontend
      run: npm run test:e2e
    
    - name: 上传 E2E 测试报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-reports
        path: |
          frontend/tests/reports/
          frontend/test-results/

  # 构建
  build:
    name: 构建应用
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, browser-extension-tests]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          frontend/package-lock.json
          browser-viewer/package-lock.json
    
    - name: 构建前端
      working-directory: frontend
      run: |
        npm ci
        npm run build
    
    - name: 构建浏览器插件
      working-directory: browser-viewer
      run: |
        npm ci
        npm run build
    
    - name: 上传构建产物
      uses: actions/upload-artifact@v3
      with:
        name: build-artifacts
        path: |
          frontend/dist/
          browser-viewer/dist/

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 运行 Trivy 漏洞扫描
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: 上传 Trivy 扫描结果
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # 部署到测试环境
  deploy-staging:
    name: 部署到测试环境
    runs-on: ubuntu-latest
    needs: [build, e2e-tests, security-scan]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 下载构建产物
      uses: actions/download-artifact@v3
      with:
        name: build-artifacts
    
    - name: 部署到测试环境
      run: |
        echo "部署到测试环境..."
        # 这里添加实际的部署脚本

  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [build, e2e-tests, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 下载构建产物
      uses: actions/download-artifact@v3
      with:
        name: build-artifacts
    
    - name: 部署到生产环境
      run: |
        echo "部署到生产环境..."
        # 这里添加实际的部署脚本
