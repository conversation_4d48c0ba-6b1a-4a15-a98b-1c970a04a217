/**
 * Playwright 全局清理
 * 在所有测试运行后执行的清理工作
 */

import { FullConfig } from '@playwright/test'
import fs from 'fs'
import path from 'path'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 开始 E2E 测试全局清理...')
  
  // 清理测试数据
  await cleanupTestData()
  
  // 生成测试报告摘要
  await generateTestSummary()
  
  // 清理临时文件（可选）
  if (process.env.CLEANUP_TEMP_FILES === 'true') {
    await cleanupTempFiles()
  }
  
  console.log('✅ E2E 测试全局清理完成')
}

async function cleanupTestData() {
  console.log('🗑️ 清理测试数据...')
  
  try {
    // 清理测试用户和数据
    // 这里应该调用实际的API来清理测试数据
    // const response = await fetch('http://localhost:8000/api/v1/test/cleanup', {
    //   method: 'DELETE',
    //   headers: { 'Content-Type': 'application/json' }
    // })
    
    console.log('✅ 测试数据清理完成')
  } catch (error) {
    console.warn('⚠️ 测试数据清理失败:', error)
  }
}

async function generateTestSummary() {
  console.log('📋 生成测试报告摘要...')
  
  try {
    const reportsDir = path.resolve('tests/reports')
    const summaryFile = path.join(reportsDir, 'test-summary.json')
    
    // 收集测试结果信息
    const summary = {
      timestamp: new Date().toISOString(),
      environment: {
        node: process.version,
        platform: process.platform,
        ci: !!process.env.CI,
      },
      reports: {
        html: 'playwright-report/index.html',
        json: 'test-results.json',
        junit: 'junit.xml',
      }
    }
    
    // 如果存在测试结果文件，读取并添加统计信息
    const resultsFile = path.join(reportsDir, 'test-results.json')
    if (fs.existsSync(resultsFile)) {
      try {
        const results = JSON.parse(fs.readFileSync(resultsFile, 'utf8'))
        summary.stats = {
          total: results.stats?.total || 0,
          passed: results.stats?.passed || 0,
          failed: results.stats?.failed || 0,
          skipped: results.stats?.skipped || 0,
          duration: results.stats?.duration || 0,
        }
      } catch (error) {
        console.warn('⚠️ 无法解析测试结果文件:', error)
      }
    }
    
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2))
    console.log('✅ 测试报告摘要已生成:', summaryFile)
  } catch (error) {
    console.warn('⚠️ 生成测试报告摘要失败:', error)
  }
}

async function cleanupTempFiles() {
  console.log('🗂️ 清理临时文件...')
  
  try {
    const tempDirs = [
      'tests/results',
      'tests/screenshots',
      'tests/videos',
      'tests/traces'
    ]
    
    for (const dir of tempDirs) {
      const fullPath = path.resolve(dir)
      if (fs.existsSync(fullPath)) {
        // 只清理文件，保留目录结构
        const files = fs.readdirSync(fullPath)
        for (const file of files) {
          const filePath = path.join(fullPath, file)
          const stat = fs.statSync(filePath)
          if (stat.isFile()) {
            fs.unlinkSync(filePath)
          }
        }
        console.log(`🗑️ 已清理目录: ${dir}`)
      }
    }
    
    console.log('✅ 临时文件清理完成')
  } catch (error) {
    console.warn('⚠️ 临时文件清理失败:', error)
  }
}

export default globalTeardown
