/**
 * 数懒平台前端主应用 - 简化测试版本
 * 基于React 18 + TypeScript + Ant Design
 */

import { ApiOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Button, Card, message, Space, Spin, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

const { Title, Text, Paragraph } = Typography;

// 简化的App组件用于测试
const App: React.FC = () => {
  const [backendStatus, setBackendStatus] = useState<'loading' | 'connected' | 'error'>('loading');
  const [apiData, setApiData] = useState<any>(null);

  // 测试后端连接
  const testBackendConnection = async () => {
    setBackendStatus('loading');
    try {
      const response = await fetch('http://localhost:8000/health');
      const data = await response.json();
      setBackendStatus('connected');
      message.success('后端连接成功！');
      console.log('后端健康检查:', data);
    } catch (error) {
      setBackendStatus('error');
      message.error('后端连接失败！');
      console.error('后端连接错误:', error);
    }
  };

  // 测试API接口
  const testApiEndpoint = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/test');
      const data = await response.json();
      setApiData(data);
      message.success('API测试成功！');
      console.log('API测试数据:', data);
    } catch (error) {
      message.error('API测试失败！');
      console.error('API测试错误:', error);
    }
  };

  // 测试浏览器插件连接接口
  const testExtensionConnection = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/v1/extension/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          extension_id: 'test_extension_123',
          browser: 'chrome',
          version: '1.0.0',
          timestamp: new Date().toISOString()
        })
      });
      const data = await response.json();
      message.success('浏览器插件连接测试成功！');
      console.log('插件连接测试:', data);
    } catch (error) {
      message.error('浏览器插件连接测试失败！');
      console.error('插件连接错误:', error);
    }
  };

  // 测试与浏览器插件的直接通信
  const testExtensionDirectCommunication = async () => {
    try {
      // 检查是否有插件环境
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        // 尝试向插件发送消息
        chrome.runtime.sendMessage('数懒智能插件ID', {
          type: 'FRONTEND_TEST_MESSAGE',
          data: {
            message: '来自前端的测试消息',
            timestamp: new Date().toISOString(),
            source: 'frontend_app'
          }
        }, (response) => {
          if (chrome.runtime.lastError) {
            message.error('插件通信失败: ' + chrome.runtime.lastError.message);
            console.error('插件通信错误:', chrome.runtime.lastError);
          } else {
            message.success('插件直接通信成功！');
            console.log('插件响应:', response);
          }
        });
      } else {
        message.warning('未检测到浏览器插件环境');
        console.warn('Chrome extension APIs not available');
      }
    } catch (error) {
      message.error('插件通信测试失败！');
      console.error('插件通信错误:', error);
    }
  };

  // 组件挂载时测试连接
  useEffect(() => {
    testBackendConnection();
  }, []);

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={1}>🚀 数懒平台 - 开发调试界面</Title>

      <Paragraph>
        这是数懒平台的前端测试界面，用于调试前后端连接和浏览器插件通信。
      </Paragraph>

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 后端连接状态 */}
        <Card title="后端服务连接状态" extra={
          <Button onClick={testBackendConnection} loading={backendStatus === 'loading'}>
            重新测试
          </Button>
        }>
          <Space>
            {backendStatus === 'loading' && <Spin />}
            {backendStatus === 'connected' && <CheckCircleOutlined style={{ color: '#52c41a' }} />}
            {backendStatus === 'error' && <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
            <Text>
              {backendStatus === 'loading' && '正在连接后端服务...'}
              {backendStatus === 'connected' && '后端服务连接正常 (http://localhost:8000)'}
              {backendStatus === 'error' && '后端服务连接失败'}
            </Text>
          </Space>
        </Card>

        {/* API测试 */}
        <Card title="API接口测试">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              type="primary"
              icon={<ApiOutlined />}
              onClick={testApiEndpoint}
              disabled={backendStatus !== 'connected'}
            >
              测试API接口
            </Button>
            {apiData && (
              <Card size="small" title="API响应数据">
                <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
                  {JSON.stringify(apiData, null, 2)}
                </pre>
              </Card>
            )}
          </Space>
        </Card>

        {/* 浏览器插件连接测试 */}
        <Card title="浏览器插件连接测试">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              type="primary"
              onClick={testExtensionConnection}
              disabled={backendStatus !== 'connected'}
            >
              测试插件连接接口
            </Button>
            <Button
              onClick={testExtensionDirectCommunication}
            >
              测试插件直接通信
            </Button>
            <Text type="secondary">
              模拟浏览器插件向后端发送连接请求，以及前端与插件的直接通信
            </Text>
          </Space>
        </Card>

        {/* 服务信息 */}
        <Card title="服务信息">
          <Space direction="vertical">
            <Text><strong>前端地址:</strong> http://localhost:3000</Text>
            <Text><strong>后端地址:</strong> http://localhost:8000</Text>
            <Text><strong>API文档:</strong> http://localhost:8000/docs</Text>
            <Text><strong>健康检查:</strong> http://localhost:8000/health</Text>
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default App;
