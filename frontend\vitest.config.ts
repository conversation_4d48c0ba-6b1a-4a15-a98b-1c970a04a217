/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  plugins: [react()],
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/components': resolve(__dirname, 'src/components'),
      '@/pages': resolve(__dirname, 'src/pages'),
      '@/stores': resolve(__dirname, 'src/stores'),
      '@/services': resolve(__dirname, 'src/services'),
      '@/types': resolve(__dirname, 'src/types'),
      '@/utils': resolve(__dirname, 'src/utils'),
      '@/hooks': resolve(__dirname, 'src/hooks'),
      '@/styles': resolve(__dirname, 'src/styles'),
    },
  },

  test: {
    // 测试环境配置
    globals: true,
    environment: 'jsdom',
    
    // 测试文件匹配模式
    include: [
      'src/**/*.{test,spec}.{js,ts,jsx,tsx}',
      'tests/**/*.{test,spec}.{js,ts,jsx,tsx}'
    ],
    
    // 排除文件
    exclude: [
      'node_modules',
      'dist',
      'build',
      'tests/e2e/**',
      '**/*.e2e.{test,spec}.{js,ts,jsx,tsx}'
    ],
    
    // 测试设置文件
    setupFiles: [
      'tests/setup/vitest.setup.ts',
      'tests/setup/dom.setup.ts'
    ],
    
    // 代码覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov', 'clover'],
      reportsDirectory: 'tests/coverage',
      include: [
        'src/**/*.{js,ts,jsx,tsx}'
      ],
      exclude: [
        'src/**/*.{test,spec}.{js,ts,jsx,tsx}',
        'src/**/*.stories.{js,ts,jsx,tsx}',
        'src/types/**',
        'src/**/*.d.ts',
        'src/main.tsx',
        'src/vite-env.d.ts'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        },
        // 组件覆盖率要求更高
        'src/components/**': {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85
        }
      }
    },
    
    // 并行测试配置
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        maxThreads: 4,
        minThreads: 1
      }
    },
    
    // 测试超时配置
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // 监听模式配置
    watch: {
      ignore: [
        'dist/**',
        'build/**',
        'node_modules/**',
        'tests/coverage/**',
        'tests/reports/**'
      ]
    },
    
    // 测试报告配置
    reporter: ['verbose', 'json', 'html'],
    outputFile: {
      json: 'tests/reports/test-results.json',
      html: 'tests/reports/test-results.html'
    },
    
    // Mock 配置
    mockReset: true,
    restoreMocks: true,
    clearMocks: true,
    
    // 环境变量
    env: {
      NODE_ENV: 'test',
      VITE_API_BASE_URL: 'http://localhost:8000',
      VITE_WS_URL: 'ws://localhost:8000/ws',
      VITE_APP_TITLE: 'Digital Lazy Platform - Test'
    },
    
    // 测试隔离配置
    isolate: true,
    
    // 快照配置
    snapshotFormat: {
      escapeString: true,
      printBasicPrototype: true
    },
    
    // 自定义匹配器
    expect: {
      // 可以在这里添加自定义匹配器
    },
    
    // 测试序列化配置
    sequence: {
      shuffle: false,
      concurrent: true
    },
    
    // 日志配置
    logHeapUsage: true,
    
    // 性能配置
    benchmark: {
      include: ['**/*.{bench,benchmark}.{js,ts,jsx,tsx}'],
      exclude: ['node_modules/**', 'dist/**'],
      reporters: ['verbose']
    }
  },

  // 构建配置（用于测试构建）
  build: {
    sourcemap: true,
    rollupOptions: {
      external: ['react', 'react-dom']
    }
  },

  // 开发服务器配置（用于测试）
  server: {
    port: 3001, // 避免与开发服务器冲突
    strictPort: true
  }
})
