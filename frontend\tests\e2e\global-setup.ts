/**
 * Playwright 全局设置
 * 在所有测试运行前执行的设置
 */

import { chromium, FullConfig } from '@playwright/test'
import path from 'path'
import fs from 'fs'

async function globalSetup(config: FullConfig) {
  console.log('🚀 开始 E2E 测试全局设置...')
  
  // 创建必要的目录
  const dirs = [
    'tests/reports',
    'tests/results',
    'tests/screenshots',
    'tests/videos',
    'tests/traces'
  ]
  
  dirs.forEach(dir => {
    const fullPath = path.resolve(dir)
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true })
      console.log(`📁 创建目录: ${dir}`)
    }
  })
  
  // 等待开发服务器启动
  const baseURL = config.projects[0].use.baseURL || 'http://localhost:3000'
  console.log(`⏳ 等待开发服务器启动: ${baseURL}`)
  
  // 检查服务器是否可用
  let retries = 30
  while (retries > 0) {
    try {
      const response = await fetch(baseURL)
      if (response.ok) {
        console.log('✅ 开发服务器已启动')
        break
      }
    } catch (error) {
      // 服务器还未启动
    }
    
    retries--
    if (retries === 0) {
      throw new Error(`❌ 开发服务器启动超时: ${baseURL}`)
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000))
  }
  
  // 创建浏览器实例进行预热
  console.log('🔥 预热浏览器...')
  const browser = await chromium.launch()
  const context = await browser.newContext()
  const page = await context.newPage()
  
  try {
    // 访问首页进行预热
    await page.goto(baseURL, { waitUntil: 'networkidle' })
    console.log('✅ 浏览器预热完成')
  } catch (error) {
    console.warn('⚠️ 浏览器预热失败:', error)
  } finally {
    await browser.close()
  }
  
  // 设置测试数据
  await setupTestData()
  
  console.log('✅ E2E 测试全局设置完成')
}

async function setupTestData() {
  console.log('📊 设置测试数据...')
  
  // 这里可以设置测试数据库、创建测试用户等
  // 例如：
  // - 清理测试数据库
  // - 创建测试用户
  // - 准备测试任务数据
  
  try {
    // 模拟API调用设置测试数据
    const testUser = {
      username: 'e2e_test_user',
      email: '<EMAIL>',
      password: 'test123456',
      fullName: 'E2E Test User'
    }
    
    // 这里应该调用实际的API来创建测试数据
    // const response = await fetch('http://localhost:8000/api/v1/test/setup', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ user: testUser })
    // })
    
    console.log('✅ 测试数据设置完成')
  } catch (error) {
    console.warn('⚠️ 测试数据设置失败:', error)
  }
}

export default globalSetup
