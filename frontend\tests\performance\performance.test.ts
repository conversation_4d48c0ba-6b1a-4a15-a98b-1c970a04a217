/**
 * 前端性能测试
 * 测试组件渲染性能、内存使用、包大小等
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, cleanup } from '@testing-library/react'
import { performance } from 'perf_hooks'

// 性能测试工具类
class PerformanceTestUtils {
  /**
   * 测量组件渲染时间
   */
  static measureRenderTime(renderFn: () => void): number {
    const start = performance.now()
    renderFn()
    const end = performance.now()
    return end - start
  }

  /**
   * 测量内存使用情况
   */
  static measureMemoryUsage(): number {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in window.performance) {
      return (window.performance as any).memory.usedJSHeapSize
    }
    return 0
  }

  /**
   * 模拟大量数据
   */
  static generateLargeDataset(size: number) {
    return Array.from({ length: size }, (_, index) => ({
      id: index,
      name: `Item ${index}`,
      description: `Description for item ${index}`,
      value: Math.random() * 1000,
      timestamp: new Date().toISOString(),
    }))
  }

  /**
   * 测试组件重渲染性能
   */
  static async measureReRenderPerformance(
    component: React.ComponentType<any>,
    props: any,
    updates: any[]
  ) {
    const renderTimes: number[] = []
    
    // 初始渲染
    const { rerender } = render(React.createElement(component, props))
    
    // 测量重渲染时间
    for (const update of updates) {
      const start = performance.now()
      rerender(React.createElement(component, { ...props, ...update }))
      const end = performance.now()
      renderTimes.push(end - start)
    }
    
    return {
      averageTime: renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length,
      maxTime: Math.max(...renderTimes),
      minTime: Math.min(...renderTimes),
      totalTime: renderTimes.reduce((a, b) => a + b, 0),
    }
  }
}

// Mock 组件用于测试
const MockTaskList = ({ tasks }: { tasks: any[] }) => (
  <div data-testid="task-list">
    {tasks.map(task => (
      <div key={task.id} data-testid={`task-${task.id}`}>
        <h3>{task.name}</h3>
        <p>{task.description}</p>
        <span>{task.value}</span>
      </div>
    ))}
  </div>
)

const MockTaskCard = ({ task, onClick }: { task: any; onClick?: () => void }) => (
  <div data-testid="task-card" onClick={onClick}>
    <h3>{task.name}</h3>
    <p>{task.description}</p>
    <button>Edit</button>
    <button>Delete</button>
  </div>
)

describe('前端性能测试', () => {
  beforeEach(() => {
    cleanup()
    vi.clearAllMocks()
  })

  describe('组件渲染性能', () => {
    it('TaskList 组件应该在合理时间内渲染', () => {
      const tasks = PerformanceTestUtils.generateLargeDataset(100)
      
      const renderTime = PerformanceTestUtils.measureRenderTime(() => {
        render(<MockTaskList tasks={tasks} />)
      })
      
      // 100个任务应该在50ms内渲染完成
      expect(renderTime).toBeLessThan(50)
    })

    it('大量数据渲染性能测试', () => {
      const largeDataset = PerformanceTestUtils.generateLargeDataset(1000)
      
      const renderTime = PerformanceTestUtils.measureRenderTime(() => {
        render(<MockTaskList tasks={largeDataset} />)
      })
      
      // 1000个任务应该在200ms内渲染完成
      expect(renderTime).toBeLessThan(200)
    })

    it('组件重渲染性能测试', async () => {
      const initialTasks = PerformanceTestUtils.generateLargeDataset(50)
      const updates = [
        { tasks: [...initialTasks, ...PerformanceTestUtils.generateLargeDataset(10)] },
        { tasks: initialTasks.slice(0, 30) },
        { tasks: [...initialTasks, { id: 999, name: 'New Task', description: 'New' }] },
      ]
      
      const performance = await PerformanceTestUtils.measureReRenderPerformance(
        MockTaskList,
        { tasks: initialTasks },
        updates
      )
      
      // 平均重渲染时间应该小于30ms
      expect(performance.averageTime).toBeLessThan(30)
      // 最大重渲染时间应该小于100ms
      expect(performance.maxTime).toBeLessThan(100)
    })
  })

  describe('内存使用测试', () => {
    it('组件卸载后应该释放内存', () => {
      const initialMemory = PerformanceTestUtils.measureMemoryUsage()
      
      // 渲染大量组件
      const tasks = PerformanceTestUtils.generateLargeDataset(500)
      const { unmount } = render(<MockTaskList tasks={tasks} />)
      
      const afterRenderMemory = PerformanceTestUtils.measureMemoryUsage()
      
      // 卸载组件
      unmount()
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc()
      }
      
      const afterUnmountMemory = PerformanceTestUtils.measureMemoryUsage()
      
      // 验证内存使用情况
      if (initialMemory > 0 && afterRenderMemory > 0 && afterUnmountMemory > 0) {
        const memoryIncrease = afterRenderMemory - initialMemory
        const memoryAfterCleanup = afterUnmountMemory - initialMemory
        
        // 卸载后内存使用应该显著减少
        expect(memoryAfterCleanup).toBeLessThan(memoryIncrease * 0.8)
      }
    })

    it('大量事件监听器不应该造成内存泄漏', () => {
      const tasks = PerformanceTestUtils.generateLargeDataset(100)
      const mockHandler = vi.fn()
      
      const { unmount } = render(
        <div>
          {tasks.map(task => (
            <MockTaskCard key={task.id} task={task} onClick={mockHandler} />
          ))}
        </div>
      )
      
      // 卸载组件
      unmount()
      
      // 验证事件监听器被正确清理
      // 这里可以添加更具体的内存泄漏检测逻辑
      expect(true).toBe(true) // 占位符断言
    })
  })

  describe('虚拟滚动性能测试', () => {
    // Mock 虚拟滚动组件
    const MockVirtualList = ({ items, itemHeight = 50 }: { items: any[]; itemHeight?: number }) => {
      const [visibleRange, setVisibleRange] = React.useState({ start: 0, end: 10 })
      
      const visibleItems = items.slice(visibleRange.start, visibleRange.end)
      
      return (
        <div 
          data-testid="virtual-list"
          style={{ height: '500px', overflow: 'auto' }}
          onScroll={(e) => {
            const scrollTop = e.currentTarget.scrollTop
            const start = Math.floor(scrollTop / itemHeight)
            const end = start + Math.ceil(500 / itemHeight)
            setVisibleRange({ start, end })
          }}
        >
          <div style={{ height: items.length * itemHeight }}>
            <div style={{ transform: `translateY(${visibleRange.start * itemHeight}px)` }}>
              {visibleItems.map((item, index) => (
                <div key={item.id} style={{ height: itemHeight }}>
                  {item.name}
                </div>
              ))}
            </div>
          </div>
        </div>
      )
    }

    it('虚拟滚动应该高效处理大量数据', () => {
      const largeDataset = PerformanceTestUtils.generateLargeDataset(10000)
      
      const renderTime = PerformanceTestUtils.measureRenderTime(() => {
        render(<MockVirtualList items={largeDataset} />)
      })
      
      // 即使有10000个项目，虚拟滚动也应该在100ms内渲染
      expect(renderTime).toBeLessThan(100)
    })
  })

  describe('异步操作性能测试', () => {
    it('并发API请求应该在合理时间内完成', async () => {
      const mockFetch = vi.fn().mockImplementation(() => 
        Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ data: 'test' })
        })
      )
      
      global.fetch = mockFetch
      
      const start = performance.now()
      
      // 模拟并发请求
      const requests = Array.from({ length: 10 }, () => 
        fetch('/api/test').then(r => r.json())
      )
      
      await Promise.all(requests)
      
      const end = performance.now()
      const totalTime = end - start
      
      // 10个并发请求应该在1秒内完成
      expect(totalTime).toBeLessThan(1000)
      expect(mockFetch).toHaveBeenCalledTimes(10)
    })

    it('状态更新批处理性能测试', async () => {
      let renderCount = 0
      
      const MockComponent = () => {
        const [count, setCount] = React.useState(0)
        const [name, setName] = React.useState('')
        
        renderCount++
        
        React.useEffect(() => {
          // 模拟快速连续的状态更新
          setCount(1)
          setName('test')
          setCount(2)
          setName('test2')
        }, [])
        
        return <div>{count} - {name}</div>
      }
      
      render(<MockComponent />)
      
      // 等待状态更新完成
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // React 应该批处理状态更新，减少渲染次数
      expect(renderCount).toBeLessThanOrEqual(3) // 初始渲染 + 批处理更新
    })
  })

  describe('包大小和加载性能', () => {
    it('动态导入应该正确工作', async () => {
      const start = performance.now()
      
      // 模拟动态导入
      const mockModule = await import('./mock-module')
      
      const end = performance.now()
      const loadTime = end - start
      
      // 动态导入应该在合理时间内完成
      expect(loadTime).toBeLessThan(100)
      expect(mockModule).toBeDefined()
    })

    it('代码分割应该减少初始包大小', () => {
      // 这里可以添加包大小分析的测试
      // 例如检查特定模块是否被正确分割
      expect(true).toBe(true) // 占位符断言
    })
  })

  describe('用户交互性能', () => {
    it('按钮点击响应应该及时', async () => {
      const mockHandler = vi.fn()
      const { getByRole } = render(
        <button onClick={mockHandler}>Click me</button>
      )
      
      const button = getByRole('button')
      
      const start = performance.now()
      
      // 模拟用户点击
      button.click()
      
      const end = performance.now()
      const responseTime = end - start
      
      // 点击响应应该在16ms内（60fps）
      expect(responseTime).toBeLessThan(16)
      expect(mockHandler).toHaveBeenCalledTimes(1)
    })

    it('表单输入响应性能测试', async () => {
      const { getByRole } = render(
        <input type="text" placeholder="Type here" />
      )
      
      const input = getByRole('textbox')
      
      const start = performance.now()
      
      // 模拟用户输入
      input.focus()
      
      const end = performance.now()
      const focusTime = end - start
      
      // 输入框聚焦应该在10ms内完成
      expect(focusTime).toBeLessThan(10)
    })
  })
})

// Mock 模块用于动态导入测试
// 这个文件应该单独创建
export const mockModuleContent = {
  name: 'mock-module',
  version: '1.0.0',
  export: () => 'Hello from mock module'
}
