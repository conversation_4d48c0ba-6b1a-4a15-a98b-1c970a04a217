/**
 * DOM 测试环境设置
 * 配置 jsdom 环境和相关 polyfills
 */

import 'resize-observer-polyfill'

// 设置 jsdom 环境的默认配置
Object.defineProperty(window, 'CSS', { value: null })
Object.defineProperty(window, 'getComputedStyle', {
  value: () => {
    return {
      display: 'none',
      appearance: ['-webkit-appearance']
    }
  }
})

Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
  configurable: true,
  value: 1,
})
Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
  configurable: true,
  value: 1,
})

// Mock Ant Design 的一些特殊需求
Object.defineProperty(window, 'getSelection', {
  value: () => ({
    removeAllRanges: () => {},
    addRange: () => {},
  }),
})

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  value: () => {},
  writable: true,
})

// Mock requestAnimationFrame
global.requestAnimationFrame = (callback: FrameRequestCallback) => {
  return setTimeout(callback, 0)
}

global.cancelAnimationFrame = (id: number) => {
  clearTimeout(id)
}

// Mock MutationObserver
global.MutationObserver = class MutationObserver {
  constructor(callback: MutationCallback) {}
  disconnect() {}
  observe(element: Element, initObject?: MutationObserverInit): void {}
  takeRecords(): MutationRecord[] { return [] }
}

// Mock DOMRect
global.DOMRect = class DOMRect {
  bottom: number = 0
  left: number = 0
  right: number = 0
  top: number = 0
  constructor(
    public x: number = 0,
    public y: number = 0,
    public width: number = 0,
    public height: number = 0,
  ) {
    this.left = x
    this.top = y
    this.right = x + width
    this.bottom = y + height
  }
  toJSON() {
    return JSON.stringify(this)
  }
}

// Mock getBoundingClientRect
Element.prototype.getBoundingClientRect = function() {
  return new DOMRect(0, 0, 0, 0)
}

// Mock scrollIntoView
Element.prototype.scrollIntoView = function() {}

// Mock focus and blur
HTMLElement.prototype.focus = function() {}
HTMLElement.prototype.blur = function() {}

// Mock createRange
document.createRange = () => {
  const range = new Range()
  
  range.getBoundingClientRect = () => new DOMRect(0, 0, 0, 0)
  range.getClientRects = () => ({
    item: () => null,
    length: 0,
    [Symbol.iterator]: function* () {},
  })
  
  return range
}

// Mock execCommand
document.execCommand = () => false

// Mock queryCommandSupported
document.queryCommandSupported = () => false

// Mock getSelection
window.getSelection = () => ({
  removeAllRanges: () => {},
  addRange: () => {},
  toString: () => '',
  rangeCount: 0,
  anchorNode: null,
  anchorOffset: 0,
  focusNode: null,
  focusOffset: 0,
  isCollapsed: true,
  type: 'None',
  getRangeAt: () => document.createRange(),
  collapse: () => {},
  extend: () => {},
  collapseToStart: () => {},
  collapseToEnd: () => {},
  selectAllChildren: () => {},
  deleteFromDocument: () => {},
  containsNode: () => false,
  modify: () => {},
}) as any

// Mock Image
global.Image = class {
  onload: (() => void) | null = null
  onerror: (() => void) | null = null
  src: string = ''
  alt: string = ''
  width: number = 0
  height: number = 0
  
  constructor(width?: number, height?: number) {
    if (width) this.width = width
    if (height) this.height = height
    
    // 模拟图片加载
    setTimeout(() => {
      if (this.onload) this.onload()
    }, 0)
  }
} as any

// Mock Canvas API
HTMLCanvasElement.prototype.getContext = function(contextId: string) {
  if (contextId === '2d') {
    return {
      fillRect: () => {},
      clearRect: () => {},
      getImageData: () => ({ data: new Array(4) }),
      putImageData: () => {},
      createImageData: () => ({ data: new Array(4) }),
      setTransform: () => {},
      drawImage: () => {},
      save: () => {},
      fillText: () => {},
      restore: () => {},
      beginPath: () => {},
      moveTo: () => {},
      lineTo: () => {},
      closePath: () => {},
      stroke: () => {},
      translate: () => {},
      scale: () => {},
      rotate: () => {},
      arc: () => {},
      fill: () => {},
      measureText: () => ({ width: 0 }),
      transform: () => {},
      rect: () => {},
      clip: () => {},
    }
  }
  return null
}

HTMLCanvasElement.prototype.toDataURL = () => ''

// Mock Audio
global.Audio = class {
  play() { return Promise.resolve() }
  pause() {}
  load() {}
  canPlayType() { return '' }
  addEventListener() {}
  removeEventListener() {}
  dispatchEvent() { return true }
  
  currentTime: number = 0
  duration: number = 0
  paused: boolean = true
  volume: number = 1
  muted: boolean = false
  src: string = ''
} as any

// Mock Video
HTMLVideoElement.prototype.play = () => Promise.resolve()
HTMLVideoElement.prototype.pause = () => {}
HTMLVideoElement.prototype.load = () => {}

// 设置默认的 viewport
Object.defineProperty(window, 'innerWidth', {
  writable: true,
  configurable: true,
  value: 1024,
})

Object.defineProperty(window, 'innerHeight', {
  writable: true,
  configurable: true,
  value: 768,
})

// Mock devicePixelRatio
Object.defineProperty(window, 'devicePixelRatio', {
  writable: true,
  configurable: true,
  value: 1,
})

// Mock screen
Object.defineProperty(window, 'screen', {
  writable: true,
  configurable: true,
  value: {
    width: 1920,
    height: 1080,
    availWidth: 1920,
    availHeight: 1040,
    colorDepth: 24,
    pixelDepth: 24,
  },
})

// Mock navigator properties
Object.defineProperty(navigator, 'userAgent', {
  writable: true,
  configurable: true,
  value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
})

Object.defineProperty(navigator, 'language', {
  writable: true,
  configurable: true,
  value: 'zh-CN',
})

Object.defineProperty(navigator, 'languages', {
  writable: true,
  configurable: true,
  value: ['zh-CN', 'zh', 'en'],
})

// Mock performance
Object.defineProperty(window, 'performance', {
  writable: true,
  configurable: true,
  value: {
    now: () => Date.now(),
    mark: () => {},
    measure: () => {},
    getEntriesByName: () => [],
    getEntriesByType: () => [],
    clearMarks: () => {},
    clearMeasures: () => {},
  },
})
