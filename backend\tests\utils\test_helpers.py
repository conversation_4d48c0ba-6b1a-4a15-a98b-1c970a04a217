"""
测试辅助工具函数
提供测试中常用的工具函数和断言方法
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi import status
from httpx import AsyncClient, Response
from sqlalchemy.orm import Session

from app.models.user import User
from app.models.task import Task
from app.schemas.user import UserCreate
from app.schemas.task import TaskCreate


class TestDataFactory:
    """测试数据工厂类"""
    
    @staticmethod
    def create_user_data(**kwargs) -> Dict[str, Any]:
        """创建用户测试数据"""
        default_data = {
            "username": f"testuser_{uuid.uuid4().hex[:8]}",
            "email": f"test_{uuid.uuid4().hex[:8]}@example.com",
            "password": "testpassword123",
            "full_name": "Test User",
            "is_active": True,
        }
        default_data.update(kwargs)
        return default_data
    
    @staticmethod
    def create_task_data(**kwargs) -> Dict[str, Any]:
        """创建任务测试数据"""
        default_data = {
            "name": f"测试任务_{uuid.uuid4().hex[:8]}",
            "description": "这是一个测试任务",
            "config": {
                "url": "https://example.com",
                "selector": ".test-selector",
                "action": "click",
                "wait_time": 1000,
            },
            "status": "pending",
            "priority": 1,
            "scheduled_at": datetime.utcnow() + timedelta(minutes=5),
        }
        default_data.update(kwargs)
        return default_data
    
    @staticmethod
    def create_auth_headers(token: str = "test_token") -> Dict[str, str]:
        """创建认证请求头"""
        return {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        }


class DatabaseTestHelper:
    """数据库测试辅助类"""
    
    @staticmethod
    def create_test_user(db: Session, **kwargs) -> User:
        """创建测试用户"""
        user_data = TestDataFactory.create_user_data(**kwargs)
        user = User(**user_data)
        db.add(user)
        db.commit()
        db.refresh(user)
        return user
    
    @staticmethod
    def create_test_task(db: Session, user_id: str, **kwargs) -> Task:
        """创建测试任务"""
        task_data = TestDataFactory.create_task_data(**kwargs)
        task_data["user_id"] = user_id
        task = Task(**task_data)
        db.add(task)
        db.commit()
        db.refresh(task)
        return task
    
    @staticmethod
    def count_records(db: Session, model_class) -> int:
        """统计记录数量"""
        return db.query(model_class).count()
    
    @staticmethod
    def clear_table(db: Session, model_class):
        """清空表数据"""
        db.query(model_class).delete()
        db.commit()


class APITestHelper:
    """API测试辅助类"""
    
    @staticmethod
    async def post_json(
        client: AsyncClient,
        url: str,
        data: Dict[str, Any],
        headers: Optional[Dict[str, str]] = None
    ) -> Response:
        """发送JSON POST请求"""
        return await client.post(
            url,
            json=data,
            headers=headers or {}
        )
    
    @staticmethod
    async def get_with_auth(
        client: AsyncClient,
        url: str,
        token: str = "test_token"
    ) -> Response:
        """发送带认证的GET请求"""
        headers = TestDataFactory.create_auth_headers(token)
        return await client.get(url, headers=headers)
    
    @staticmethod
    def assert_response_success(response: Response, expected_status: int = status.HTTP_200_OK):
        """断言响应成功"""
        assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}: {response.text}"
    
    @staticmethod
    def assert_response_error(response: Response, expected_status: int):
        """断言响应错误"""
        assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}: {response.text}"
    
    @staticmethod
    def assert_json_response(response: Response, expected_keys: List[str]):
        """断言JSON响应包含指定键"""
        data = response.json()
        for key in expected_keys:
            assert key in data, f"Key '{key}' not found in response: {data}"
    
    @staticmethod
    def assert_pagination_response(response: Response):
        """断言分页响应格式"""
        APITestHelper.assert_response_success(response)
        data = response.json()
        required_keys = ["items", "total", "page", "size", "pages"]
        APITestHelper.assert_json_response(response, required_keys)
        
        assert isinstance(data["items"], list)
        assert isinstance(data["total"], int)
        assert isinstance(data["page"], int)
        assert isinstance(data["size"], int)
        assert isinstance(data["pages"], int)


class MockHelper:
    """Mock辅助类"""
    
    @staticmethod
    def create_async_mock(return_value: Any = None) -> AsyncMock:
        """创建异步Mock"""
        mock = AsyncMock()
        if return_value is not None:
            mock.return_value = return_value
        return mock
    
    @staticmethod
    def create_redis_mock() -> MagicMock:
        """创建Redis Mock"""
        mock_redis = MagicMock()
        mock_redis.get = AsyncMock(return_value=None)
        mock_redis.set = AsyncMock(return_value=True)
        mock_redis.delete = AsyncMock(return_value=True)
        mock_redis.exists = AsyncMock(return_value=False)
        mock_redis.expire = AsyncMock(return_value=True)
        mock_redis.hget = AsyncMock(return_value=None)
        mock_redis.hset = AsyncMock(return_value=True)
        mock_redis.hdel = AsyncMock(return_value=True)
        mock_redis.lpush = AsyncMock(return_value=1)
        mock_redis.rpop = AsyncMock(return_value=None)
        mock_redis.llen = AsyncMock(return_value=0)
        return mock_redis
    
    @staticmethod
    def create_auth_service_mock() -> MagicMock:
        """创建认证服务Mock"""
        mock_service = MagicMock()
        mock_service.create_access_token = MagicMock(return_value="test_token")
        mock_service.verify_token = AsyncMock(return_value={"sub": "test_user"})
        mock_service.hash_password = MagicMock(return_value="hashed_password")
        mock_service.verify_password = MagicMock(return_value=True)
        return mock_service


class ValidationHelper:
    """验证辅助类"""
    
    @staticmethod
    def validate_uuid(value: str) -> bool:
        """验证UUID格式"""
        try:
            uuid.UUID(value)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_datetime_string(value: str) -> bool:
        """验证ISO格式日期时间字符串"""
        try:
            datetime.fromisoformat(value.replace('Z', '+00:00'))
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """简单的邮箱格式验证"""
        return "@" in email and "." in email.split("@")[1]
    
    @staticmethod
    def validate_json_structure(data: Dict[str, Any], required_fields: List[str]) -> bool:
        """验证JSON结构"""
        return all(field in data for field in required_fields)


class PerformanceHelper:
    """性能测试辅助类"""
    
    @staticmethod
    def assert_response_time(response: Response, max_time_ms: int = 1000):
        """断言响应时间"""
        # 注意：httpx的Response对象可能没有elapsed属性
        # 这里需要根据实际情况调整
        pass
    
    @staticmethod
    def measure_execution_time(func, *args, **kwargs) -> tuple:
        """测量函数执行时间"""
        import time
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = (end_time - start_time) * 1000  # 转换为毫秒
        return result, execution_time


# 常用断言函数
def assert_valid_uuid(value: str, message: str = "Invalid UUID format"):
    """断言有效的UUID"""
    assert ValidationHelper.validate_uuid(value), message


def assert_valid_datetime(value: str, message: str = "Invalid datetime format"):
    """断言有效的日期时间"""
    assert ValidationHelper.validate_datetime_string(value), message


def assert_valid_email(email: str, message: str = "Invalid email format"):
    """断言有效的邮箱"""
    assert ValidationHelper.validate_email(email), message


def assert_dict_contains(data: Dict[str, Any], expected: Dict[str, Any], message: str = "Dict does not contain expected values"):
    """断言字典包含期望的键值对"""
    for key, value in expected.items():
        assert key in data, f"Key '{key}' not found in data"
        assert data[key] == value, f"Expected {key}={value}, got {data[key]}"


# 测试装饰器
def skip_if_no_database(func):
    """如果没有数据库连接则跳过测试"""
    import os
    return pytest.mark.skipif(
        os.getenv("SKIP_DATABASE_TESTS") == "true",
        reason="Database tests are disabled"
    )(func)


def skip_if_no_redis(func):
    """如果没有Redis连接则跳过测试"""
    import os
    return pytest.mark.skipif(
        os.getenv("SKIP_REDIS_TESTS") == "true",
        reason="Redis tests are disabled"
    )(func)
