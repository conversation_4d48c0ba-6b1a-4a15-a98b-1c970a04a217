version: '3.8'

# 数懒平台监控服务配置
# 包含 Prometheus、<PERSON><PERSON>、<PERSON><PERSON><PERSON> 等监控组件

services:
  # Prometheus - 指标收集
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: digital-lazy-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - monitoring
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prometheus.localhost`)"

  # Grafana - 可视化仪表板
  grafana:
    image: grafana/grafana:10.0.0
    container_name: digital-lazy-grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
      - GF_FEATURE_TOGGLES_ENABLE=ngalert
    networks:
      - monitoring
    restart: unless-stopped
    depends_on:
      - prometheus
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.localhost`)"

  # AlertManager - 告警管理
  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: digital-lazy-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - monitoring
    restart: unless-stopped

  # Node Exporter - 系统指标
  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: digital-lazy-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring
    restart: unless-stopped

  # cAdvisor - 容器指标
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: digital-lazy-cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - monitoring
    restart: unless-stopped

  # Jaeger - 分布式追踪
  jaeger:
    image: jaegertracing/all-in-one:1.46
    container_name: digital-lazy-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - monitoring
    restart: unless-stopped

  # Elasticsearch - 日志存储
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: digital-lazy-elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - monitoring
    restart: unless-stopped

  # Kibana - 日志可视化
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: digital-lazy-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    networks:
      - monitoring
    restart: unless-stopped
    depends_on:
      - elasticsearch

  # Logstash - 日志处理
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: digital-lazy-logstash
    ports:
      - "5044:5044"
      - "9600:9600"
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./monitoring/logstash/config:/usr/share/logstash/config
    networks:
      - monitoring
    restart: unless-stopped
    depends_on:
      - elasticsearch

  # Redis Exporter - Redis 指标
  redis-exporter:
    image: oliver006/redis_exporter:v1.51.0
    container_name: digital-lazy-redis-exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis:6379
    networks:
      - monitoring
      - default
    restart: unless-stopped

  # Postgres Exporter - PostgreSQL 指标
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.12.0
    container_name: digital-lazy-postgres-exporter
    ports:
      - "9187:9187"
    environment:
      - DATA_SOURCE_NAME=********************************************/digital_lazy?sslmode=disable
    networks:
      - monitoring
      - default
    restart: unless-stopped

  # Blackbox Exporter - 端点监控
  blackbox-exporter:
    image: prom/blackbox-exporter:v0.24.0
    container_name: digital-lazy-blackbox-exporter
    ports:
      - "9115:9115"
    volumes:
      - ./monitoring/blackbox/blackbox.yml:/etc/blackbox_exporter/config.yml
    networks:
      - monitoring
    restart: unless-stopped

  # Loki - 日志聚合
  loki:
    image: grafana/loki:2.8.0
    container_name: digital-lazy-loki
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki/loki.yml:/etc/loki/local-config.yaml
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - monitoring
    restart: unless-stopped

  # Promtail - 日志收集
  promtail:
    image: grafana/promtail:2.8.0
    container_name: digital-lazy-promtail
    volumes:
      - ./monitoring/promtail/promtail.yml:/etc/promtail/config.yml
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - monitoring
    restart: unless-stopped
    depends_on:
      - loki

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  alertmanager_data:
    driver: local
  elasticsearch_data:
    driver: local
  loki_data:
    driver: local

networks:
  monitoring:
    driver: bridge
  default:
    external: true
    name: digital-lazy_default
