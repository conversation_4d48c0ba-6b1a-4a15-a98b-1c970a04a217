{
  "name": "digital-lazy-platform-frontend",
  "version": "1.0.0",
  "description": "数懒平台前端管理界面 - 基于React的现代化Web应用",
  "private": true,
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext ts,tsx --fix",
    "type-check": "tsc --noEmit",

    // 测试脚本
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "test:watch": "vitest --watch",
    "test:run": "vitest run",
    "test:unit": "vitest run src",
    "test:integration": "vitest run tests/integration",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:e2e:headed": "playwright test --headed",
    "test:performance": "lighthouse http://localhost:3000 --output=json --output-path=./tests/reports/lighthouse.json",

    // 测试相关工具
    "test:setup": "playwright install",
    "test:clean": "rimraf tests/coverage tests/reports",
    "test:ci": "npm run test:run && npm run test:e2e"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.15.0",
    "antd": "^5.8.6",
    "@ant-design/icons": "^5.2.6",
    "zustand": "^4.4.1",
    "@tanstack/react-query": "^4.32.6",
    "axios": "^1.5.0",
    "echarts": "^5.4.3",
    "echarts-for-react": "^3.0.2",
    "dayjs": "^1.11.9",
    "lodash-es": "^4.17.21",
    "socket.io-client": "^4.7.2",
    "react-hook-form": "^7.45.4",
    "@hookform/resolvers": "^3.3.1",
    "zod": "^3.22.2",
    "immer": "^10.0.2",
    "ahooks": "^3.7.8"
  },
  "devDependencies": {
    "@types/react": "^18.2.15",
    "@types/react-dom": "^18.2.7",
    "@types/lodash-es": "^4.17.8",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.3",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.3",
    "typescript": "^5.0.2",
    "vite": "^4.4.5",

    // 测试框架
    "vitest": "^0.34.0",
    "@vitest/ui": "^0.34.0",
    "jsdom": "^22.1.0",
    "happy-dom": "^10.0.3",

    // React测试工具
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^6.1.0",
    "@testing-library/user-event": "^14.4.3",

    // Mock和测试工具
    "msw": "^1.3.0",
    "@faker-js/faker": "^8.0.2",
    "resize-observer-polyfill": "^1.5.1",

    // E2E测试
    "@playwright/test": "^1.37.0",

    // 覆盖率
    "@vitest/coverage-v8": "^0.34.0",

    // 性能测试
    "lighthouse": "^11.0.0",
    "web-vitals": "^3.4.0",
    "@vitest/ui": "^0.34.0",
    "jsdom": "^22.1.0",
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^5.17.0",
    "autoprefixer": "^10.4.15",
    "postcss": "^8.4.28",
    "tailwindcss": "^3.3.3"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  },
  "browserslist": {
    "production": [
      ">0.2%",
      "not dead",
      "not op_mini all"
    ],
    "development": [
      "last 1 chrome version",
      "last 1 firefox version",
      "last 1 safari version"
    ]
  }
}
