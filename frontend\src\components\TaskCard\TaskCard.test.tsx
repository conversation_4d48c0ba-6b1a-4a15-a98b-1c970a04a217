/**
 * TaskCard 组件测试
 * 测试任务卡片组件的渲染和交互功能
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import { render, mockTask, createUserEvent } from '../../tests/utils/test-utils'
import TaskCard from './TaskCard'

// Mock TaskCard 组件（如果还没有实现）
const MockTaskCard = ({ task, onEdit, onDelete, onStart, onPause }: any) => (
  <div data-testid="task-card" className="task-card">
    <div data-testid="task-name">{task.name}</div>
    <div data-testid="task-description">{task.description}</div>
    <div data-testid="task-status">{task.status}</div>
    <div data-testid="task-priority">优先级: {task.priority}</div>
    
    <div className="task-actions">
      <button data-testid="edit-button" onClick={() => onEdit?.(task)}>
        编辑
      </button>
      <button data-testid="delete-button" onClick={() => onDelete?.(task)}>
        删除
      </button>
      {task.status === 'pending' && (
        <button data-testid="start-button" onClick={() => onStart?.(task)}>
          开始
        </button>
      )}
      {task.status === 'running' && (
        <button data-testid="pause-button" onClick={() => onPause?.(task)}>
          暂停
        </button>
      )}
    </div>
  </div>
)

// 如果 TaskCard 组件还没有实现，使用 Mock 版本
const TaskCard = MockTaskCard

describe('TaskCard', () => {
  const mockHandlers = {
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    onStart: vi.fn(),
    onPause: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('应该正确渲染任务信息', () => {
    render(
      <TaskCard 
        task={mockTask} 
        {...mockHandlers}
      />
    )

    expect(screen.getByTestId('task-name')).toHaveTextContent(mockTask.name)
    expect(screen.getByTestId('task-description')).toHaveTextContent(mockTask.description)
    expect(screen.getByTestId('task-status')).toHaveTextContent(mockTask.status)
    expect(screen.getByTestId('task-priority')).toHaveTextContent(`优先级: ${mockTask.priority}`)
  })

  it('应该显示正确的操作按钮', () => {
    render(
      <TaskCard 
        task={mockTask} 
        {...mockHandlers}
      />
    )

    expect(screen.getByTestId('edit-button')).toBeInTheDocument()
    expect(screen.getByTestId('delete-button')).toBeInTheDocument()
    
    // pending 状态应该显示开始按钮
    if (mockTask.status === 'pending') {
      expect(screen.getByTestId('start-button')).toBeInTheDocument()
      expect(screen.queryByTestId('pause-button')).not.toBeInTheDocument()
    }
  })

  it('运行中的任务应该显示暂停按钮', () => {
    const runningTask = { ...mockTask, status: 'running' as const }
    
    render(
      <TaskCard 
        task={runningTask} 
        {...mockHandlers}
      />
    )

    expect(screen.getByTestId('pause-button')).toBeInTheDocument()
    expect(screen.queryByTestId('start-button')).not.toBeInTheDocument()
  })

  it('点击编辑按钮应该调用 onEdit 回调', async () => {
    const user = await createUserEvent()
    
    render(
      <TaskCard 
        task={mockTask} 
        {...mockHandlers}
      />
    )

    const editButton = screen.getByTestId('edit-button')
    await user.click(editButton)

    expect(mockHandlers.onEdit).toHaveBeenCalledTimes(1)
    expect(mockHandlers.onEdit).toHaveBeenCalledWith(mockTask)
  })

  it('点击删除按钮应该调用 onDelete 回调', async () => {
    const user = await createUserEvent()
    
    render(
      <TaskCard 
        task={mockTask} 
        {...mockHandlers}
      />
    )

    const deleteButton = screen.getByTestId('delete-button')
    await user.click(deleteButton)

    expect(mockHandlers.onDelete).toHaveBeenCalledTimes(1)
    expect(mockHandlers.onDelete).toHaveBeenCalledWith(mockTask)
  })

  it('点击开始按钮应该调用 onStart 回调', async () => {
    const user = await createUserEvent()
    
    render(
      <TaskCard 
        task={mockTask} 
        {...mockHandlers}
      />
    )

    const startButton = screen.getByTestId('start-button')
    await user.click(startButton)

    expect(mockHandlers.onStart).toHaveBeenCalledTimes(1)
    expect(mockHandlers.onStart).toHaveBeenCalledWith(mockTask)
  })

  it('点击暂停按钮应该调用 onPause 回调', async () => {
    const user = await createUserEvent()
    const runningTask = { ...mockTask, status: 'running' as const }
    
    render(
      <TaskCard 
        task={runningTask} 
        {...mockHandlers}
      />
    )

    const pauseButton = screen.getByTestId('pause-button')
    await user.click(pauseButton)

    expect(mockHandlers.onPause).toHaveBeenCalledTimes(1)
    expect(mockHandlers.onPause).toHaveBeenCalledWith(runningTask)
  })

  it('应该正确应用CSS类名', () => {
    render(
      <TaskCard 
        task={mockTask} 
        {...mockHandlers}
      />
    )

    const taskCard = screen.getByTestId('task-card')
    expect(taskCard).toHaveClass('task-card')
  })

  it('应该支持自定义className', () => {
    const customClass = 'custom-task-card'
    
    render(
      <TaskCard 
        task={mockTask} 
        className={customClass}
        {...mockHandlers}
      />
    )

    const taskCard = screen.getByTestId('task-card')
    expect(taskCard).toHaveClass(customClass)
  })

  it('应该正确处理缺失的回调函数', () => {
    // 不传递任何回调函数
    render(<TaskCard task={mockTask} />)

    const editButton = screen.getByTestId('edit-button')
    
    // 点击按钮不应该抛出错误
    expect(() => {
      fireEvent.click(editButton)
    }).not.toThrow()
  })

  it('应该正确显示不同优先级的任务', () => {
    const highPriorityTask = { ...mockTask, priority: 5 }
    
    render(
      <TaskCard 
        task={highPriorityTask} 
        {...mockHandlers}
      />
    )

    expect(screen.getByTestId('task-priority')).toHaveTextContent('优先级: 5')
  })

  it('应该正确处理长文本内容', () => {
    const longTextTask = {
      ...mockTask,
      name: '这是一个非常长的任务名称，用来测试组件如何处理长文本内容的显示和布局',
      description: '这是一个非常长的任务描述，包含了很多详细信息，用来测试组件在处理长文本时的表现，确保不会破坏布局或影响用户体验。'
    }
    
    render(
      <TaskCard 
        task={longTextTask} 
        {...mockHandlers}
      />
    )

    expect(screen.getByTestId('task-name')).toHaveTextContent(longTextTask.name)
    expect(screen.getByTestId('task-description')).toHaveTextContent(longTextTask.description)
  })

  it('应该支持键盘导航', async () => {
    const user = await createUserEvent()
    
    render(
      <TaskCard 
        task={mockTask} 
        {...mockHandlers}
      />
    )

    const editButton = screen.getByTestId('edit-button')
    
    // 使用 Tab 键导航到按钮
    await user.tab()
    expect(editButton).toHaveFocus()
    
    // 使用 Enter 键激活按钮
    await user.keyboard('{Enter}')
    expect(mockHandlers.onEdit).toHaveBeenCalledTimes(1)
  })

  it('应该正确处理任务状态变化', () => {
    const { rerender } = render(
      <TaskCard 
        task={mockTask} 
        {...mockHandlers}
      />
    )

    // 初始状态
    expect(screen.getByTestId('task-status')).toHaveTextContent('pending')
    expect(screen.getByTestId('start-button')).toBeInTheDocument()

    // 更新为运行状态
    const runningTask = { ...mockTask, status: 'running' as const }
    rerender(
      <TaskCard 
        task={runningTask} 
        {...mockHandlers}
      />
    )

    expect(screen.getByTestId('task-status')).toHaveTextContent('running')
    expect(screen.getByTestId('pause-button')).toBeInTheDocument()
    expect(screen.queryByTestId('start-button')).not.toBeInTheDocument()
  })
})
