/**
 * 浏览器插件扩展Mock设置
 * 为测试环境提供更完整的浏览器插件API模拟
 */

import { vi } from 'vitest'

// 扩展Chrome API Mock
const createExtendedChromeMock = () => {
  const chromeMock = {
    // Runtime API
    runtime: {
      id: 'test-extension-id',
      getManifest: vi.fn(() => ({
        name: 'Digital Lazy Browser Viewer',
        version: '1.0.0',
        manifest_version: 3,
      })),
      sendMessage: vi.fn(),
      onMessage: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
        hasListener: vi.fn(),
      },
      connect: vi.fn(() => ({
        postMessage: vi.fn(),
        disconnect: vi.fn(),
        onMessage: {
          addListener: vi.fn(),
          removeListener: vi.fn(),
        },
        onDisconnect: {
          addListener: vi.fn(),
          removeListener: vi.fn(),
        },
      })),
      onConnect: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
      getURL: vi.fn((path: string) => `chrome-extension://test-id/${path}`),
      lastError: null,
    },

    // Tabs API
    tabs: {
      query: vi.fn(),
      get: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      remove: vi.fn(),
      sendMessage: vi.fn(),
      executeScript: vi.fn(),
      insertCSS: vi.fn(),
      onCreated: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
      onUpdated: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
      onRemoved: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
      onActivated: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
    },

    // Storage API
    storage: {
      local: {
        get: vi.fn(),
        set: vi.fn(),
        remove: vi.fn(),
        clear: vi.fn(),
        getBytesInUse: vi.fn(),
        onChanged: {
          addListener: vi.fn(),
          removeListener: vi.fn(),
        },
      },
      sync: {
        get: vi.fn(),
        set: vi.fn(),
        remove: vi.fn(),
        clear: vi.fn(),
        getBytesInUse: vi.fn(),
        onChanged: {
          addListener: vi.fn(),
          removeListener: vi.fn(),
        },
      },
      session: {
        get: vi.fn(),
        set: vi.fn(),
        remove: vi.fn(),
        clear: vi.fn(),
      },
      onChanged: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
    },

    // Action API (替代 browserAction)
    action: {
      setTitle: vi.fn(),
      getTitle: vi.fn(),
      setIcon: vi.fn(),
      setPopup: vi.fn(),
      getPopup: vi.fn(),
      setBadgeText: vi.fn(),
      getBadgeText: vi.fn(),
      setBadgeBackgroundColor: vi.fn(),
      getBadgeBackgroundColor: vi.fn(),
      enable: vi.fn(),
      disable: vi.fn(),
      onClicked: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
    },

    // Scripting API (Manifest V3)
    scripting: {
      executeScript: vi.fn(),
      insertCSS: vi.fn(),
      removeCSS: vi.fn(),
      registerContentScripts: vi.fn(),
      unregisterContentScripts: vi.fn(),
      getRegisteredContentScripts: vi.fn(),
    },

    // Permissions API
    permissions: {
      contains: vi.fn(),
      getAll: vi.fn(),
      request: vi.fn(),
      remove: vi.fn(),
      onAdded: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
      onRemoved: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
    },

    // Alarms API
    alarms: {
      create: vi.fn(),
      get: vi.fn(),
      getAll: vi.fn(),
      clear: vi.fn(),
      clearAll: vi.fn(),
      onAlarm: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
    },

    // Notifications API
    notifications: {
      create: vi.fn(),
      update: vi.fn(),
      clear: vi.fn(),
      getAll: vi.fn(),
      getPermissionLevel: vi.fn(),
      onClosed: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
      onClicked: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
      onButtonClicked: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
    },

    // Context Menus API
    contextMenus: {
      create: vi.fn(),
      update: vi.fn(),
      remove: vi.fn(),
      removeAll: vi.fn(),
      onClicked: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
    },

    // Cookies API
    cookies: {
      get: vi.fn(),
      getAll: vi.fn(),
      set: vi.fn(),
      remove: vi.fn(),
      getAllCookieStores: vi.fn(),
      onChanged: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
      },
    },

    // WebRequest API
    webRequest: {
      onBeforeRequest: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
        hasListener: vi.fn(),
      },
      onBeforeSendHeaders: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
        hasListener: vi.fn(),
      },
      onSendHeaders: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
        hasListener: vi.fn(),
      },
      onHeadersReceived: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
        hasListener: vi.fn(),
      },
      onAuthRequired: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
        hasListener: vi.fn(),
      },
      onResponseStarted: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
        hasListener: vi.fn(),
      },
      onBeforeRedirect: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
        hasListener: vi.fn(),
      },
      onCompleted: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
        hasListener: vi.fn(),
      },
      onErrorOccurred: {
        addListener: vi.fn(),
        removeListener: vi.fn(),
        hasListener: vi.fn(),
      },
    },

    // Offscreen API (Manifest V3)
    offscreen: {
      createDocument: vi.fn(),
      closeDocument: vi.fn(),
      hasDocument: vi.fn(),
    },

    // Side Panel API
    sidePanel: {
      open: vi.fn(),
      setOptions: vi.fn(),
      getOptions: vi.fn(),
      setPanelBehavior: vi.fn(),
    },
  }

  return chromeMock
}

// 设置全局Chrome API Mock
const chromeMock = createExtendedChromeMock()
Object.defineProperty(globalThis, 'chrome', {
  value: chromeMock,
  writable: true,
  configurable: true,
})

// 设置Browser API Mock (WebExtensions标准)
Object.defineProperty(globalThis, 'browser', {
  value: chromeMock,
  writable: true,
  configurable: true,
})

// Mock Web APIs
Object.defineProperty(globalThis, 'MutationObserver', {
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    disconnect: vi.fn(),
    takeRecords: vi.fn(() => []),
  })),
  writable: true,
})

Object.defineProperty(globalThis, 'IntersectionObserver', {
  value: vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  })),
  writable: true,
})

// Mock Blob和File API
Object.defineProperty(globalThis, 'Blob', {
  value: vi.fn().mockImplementation((parts, options) => ({
    size: parts?.reduce((acc: number, part: any) => acc + (part?.length || 0), 0) || 0,
    type: options?.type || '',
    slice: vi.fn(),
    stream: vi.fn(),
    text: vi.fn(),
    arrayBuffer: vi.fn(),
  })),
  writable: true,
})

// Mock Canvas API
const mockCanvas = {
  getContext: vi.fn(() => ({
    fillRect: vi.fn(),
    clearRect: vi.fn(),
    getImageData: vi.fn(() => ({ data: new Uint8ClampedArray(4) })),
    putImageData: vi.fn(),
    createImageData: vi.fn(() => ({ data: new Uint8ClampedArray(4) })),
    setTransform: vi.fn(),
    drawImage: vi.fn(),
    save: vi.fn(),
    restore: vi.fn(),
    beginPath: vi.fn(),
    moveTo: vi.fn(),
    lineTo: vi.fn(),
    closePath: vi.fn(),
    stroke: vi.fn(),
    fill: vi.fn(),
    arc: vi.fn(),
    rect: vi.fn(),
    fillText: vi.fn(),
    measureText: vi.fn(() => ({ width: 0 })),
  })),
  toDataURL: vi.fn(() => 'data:image/png;base64,'),
  toBlob: vi.fn(),
  width: 300,
  height: 150,
}

Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  value: mockCanvas.getContext,
  writable: true,
})

Object.defineProperty(HTMLCanvasElement.prototype, 'toDataURL', {
  value: mockCanvas.toDataURL,
  writable: true,
})

// 导出Mock对象供测试使用
export { chromeMock }

// 测试工具函数
export const createMockTab = (overrides: Partial<chrome.tabs.Tab> = {}): chrome.tabs.Tab => ({
  id: 1,
  index: 0,
  windowId: 1,
  highlighted: false,
  active: true,
  pinned: false,
  incognito: false,
  selected: true,
  discarded: false,
  autoDiscardable: true,
  groupId: -1,
  url: 'https://example.com',
  title: 'Test Page',
  favIconUrl: 'https://example.com/favicon.ico',
  status: 'complete',
  ...overrides,
})

export const createMockMessage = (type: string, data: any = {}) => ({
  type,
  data,
  timestamp: Date.now(),
  id: Math.random().toString(36).substr(2, 9),
})

export const mockStorageData = (data: Record<string, any>) => {
  chromeMock.storage.local.get.mockImplementation((keys, callback) => {
    if (typeof keys === 'function') {
      keys(data)
    } else if (callback) {
      const result = keys ? 
        Object.fromEntries(Object.entries(data).filter(([key]) => keys.includes(key))) :
        data
      callback(result)
    }
    return Promise.resolve(data)
  })
}

export const resetAllMocks = () => {
  Object.values(chromeMock).forEach(api => {
    if (typeof api === 'object' && api !== null) {
      Object.values(api).forEach(method => {
        if (vi.isMockFunction(method)) {
          method.mockReset()
        }
      })
    }
  })
}
