# 数懒平台告警规则
# 定义各种监控指标的告警条件

groups:
  # 应用服务告警
  - name: digital-lazy-application
    rules:
      # 服务不可用告警
      - alert: ServiceDown
        expr: up{job=~"digital-lazy-.*"} == 0
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "数懒平台服务不可用"
          description: "服务 {{ $labels.job }} 在实例 {{ $labels.instance }} 上已经停止运行超过1分钟"

      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "高错误率检测"
          description: "服务 {{ $labels.job }} 的错误率在过去5分钟内超过10%，当前值: {{ $value | humanizePercentage }}"

      # 响应时间过长告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "响应时间过长"
          description: "服务 {{ $labels.job }} 的95%响应时间超过1秒，当前值: {{ $value }}s"

      # API请求量异常告警
      - alert: UnusualRequestVolume
        expr: rate(http_requests_total[5m]) > 100 or rate(http_requests_total[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          service: "{{ $labels.job }}"
        annotations:
          summary: "API请求量异常"
          description: "服务 {{ $labels.job }} 的请求量异常，当前值: {{ $value }} req/s"

  # 系统资源告警
  - name: digital-lazy-system
    rules:
      # CPU使用率过高
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "实例 {{ $labels.instance }} 的CPU使用率超过80%，当前值: {{ $value | humanizePercentage }}"

      # 内存使用率过高
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "实例 {{ $labels.instance }} 的内存使用率超过85%，当前值: {{ $value | humanizePercentage }}"

      # 磁盘空间不足
      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间不足"
          description: "实例 {{ $labels.instance }} 的磁盘 {{ $labels.mountpoint }} 使用率超过90%，当前值: {{ $value | humanizePercentage }}"

      # 磁盘IO过高
      - alert: HighDiskIO
        expr: rate(node_disk_io_time_seconds_total[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "磁盘IO过高"
          description: "实例 {{ $labels.instance }} 的磁盘IO使用率过高，当前值: {{ $value | humanizePercentage }}"

  # 数据库告警
  - name: digital-lazy-database
    rules:
      # PostgreSQL连接数过多
      - alert: PostgreSQLTooManyConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL连接数过多"
          description: "PostgreSQL数据库连接数超过最大连接数的80%，当前值: {{ $value | humanizePercentage }}"

      # PostgreSQL慢查询
      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL慢查询检测"
          description: "PostgreSQL数据库可能存在慢查询，查询效率较低"

      # Redis内存使用过高
      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis内存使用过高"
          description: "Redis内存使用率超过90%，当前值: {{ $value | humanizePercentage }}"

      # Redis连接数过多
      - alert: RedisTooManyConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis连接数过多"
          description: "Redis连接数超过100，当前值: {{ $value }}"

  # 容器告警
  - name: digital-lazy-containers
    rules:
      # 容器重启频繁
      - alert: ContainerRestartingFrequently
        expr: rate(container_last_seen[5m]) > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "容器重启频繁"
          description: "容器 {{ $labels.name }} 在过去5分钟内重启频繁"

      # 容器内存使用过高
      - alert: ContainerHighMemoryUsage
        expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) * 100 > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "容器内存使用过高"
          description: "容器 {{ $labels.name }} 内存使用率超过90%，当前值: {{ $value | humanizePercentage }}"

      # 容器CPU使用过高
      - alert: ContainerHighCPUUsage
        expr: (rate(container_cpu_usage_seconds_total[5m]) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "容器CPU使用过高"
          description: "容器 {{ $labels.name }} CPU使用率超过80%，当前值: {{ $value | humanizePercentage }}"

  # 业务指标告警
  - name: digital-lazy-business
    rules:
      # 任务失败率过高
      - alert: HighTaskFailureRate
        expr: rate(digital_lazy_tasks_failed_total[5m]) / rate(digital_lazy_tasks_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "任务失败率过高"
          description: "任务失败率在过去5分钟内超过10%，当前值: {{ $value | humanizePercentage }}"

      # 任务队列积压
      - alert: TaskQueueBacklog
        expr: digital_lazy_task_queue_size > 100
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "任务队列积压"
          description: "任务队列中待处理任务数量超过100，当前值: {{ $value }}"

      # 用户登录失败率过高
      - alert: HighLoginFailureRate
        expr: rate(digital_lazy_login_failures_total[5m]) / rate(digital_lazy_login_attempts_total[5m]) > 0.2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "用户登录失败率过高"
          description: "用户登录失败率在过去5分钟内超过20%，可能存在安全问题"

      # 浏览器插件连接异常
      - alert: ExtensionConnectionIssues
        expr: rate(digital_lazy_extension_connection_errors_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "浏览器插件连接异常"
          description: "浏览器插件连接错误率过高，当前值: {{ $value }} errors/s"

  # 安全告警
  - name: digital-lazy-security
    rules:
      # 异常登录尝试
      - alert: SuspiciousLoginActivity
        expr: rate(digital_lazy_login_attempts_total[1m]) > 10
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "检测到异常登录活动"
          description: "在过去1分钟内检测到异常的登录尝试，可能存在暴力破解攻击"

      # API调用频率异常
      - alert: UnusualAPIActivity
        expr: rate(http_requests_total[1m]) > 1000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "API调用频率异常"
          description: "检测到异常的API调用频率，可能存在DDoS攻击或异常使用"
