/**
 * Vitest 全局测试设置
 * 配置测试环境、Mock服务、全局变量等
 */

import { expect, afterEach, beforeAll, afterAll, vi } from 'vitest'
import { cleanup } from '@testing-library/react'
import '@testing-library/jest-dom'

// 扩展 expect 匹配器
import * as matchers from '@testing-library/jest-dom/matchers'
expect.extend(matchers)

// 每个测试后清理
afterEach(() => {
  cleanup()
  vi.clearAllMocks()
  vi.clearAllTimers()
})

// 全局测试设置
beforeAll(() => {
  // 设置测试环境变量
  process.env.NODE_ENV = 'test'
  
  // Mock console 方法以减少测试输出噪音
  vi.spyOn(console, 'warn').mockImplementation(() => {})
  vi.spyOn(console, 'error').mockImplementation(() => {})
  
  // Mock window.matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))

  // Mock window.location
  Object.defineProperty(window, 'location', {
    value: {
      href: 'http://localhost:3000',
      origin: 'http://localhost:3000',
      protocol: 'http:',
      host: 'localhost:3000',
      hostname: 'localhost',
      port: '3000',
      pathname: '/',
      search: '',
      hash: '',
      assign: vi.fn(),
      replace: vi.fn(),
      reload: vi.fn(),
    },
    writable: true,
  })

  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn(),
  }
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true,
  })

  // Mock sessionStorage
  const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn(),
  }
  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
    writable: true,
  })

  // Mock fetch
  global.fetch = vi.fn()

  // Mock WebSocket
  global.WebSocket = vi.fn().mockImplementation(() => ({
    close: vi.fn(),
    send: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    readyState: WebSocket.CONNECTING,
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3,
  }))

  // Mock URL.createObjectURL
  global.URL.createObjectURL = vi.fn(() => 'mocked-url')
  global.URL.revokeObjectURL = vi.fn()

  // Mock File API
  global.File = class MockFile {
    constructor(
      public chunks: any[],
      public name: string,
      public options: any = {}
    ) {}
    get size() { return 1024 }
    get type() { return this.options.type || 'text/plain' }
    get lastModified() { return Date.now() }
  } as any

  global.FileReader = class MockFileReader {
    result: any = null
    error: any = null
    readyState: number = 0
    onload: any = null
    onerror: any = null
    onabort: any = null
    onloadstart: any = null
    onloadend: any = null
    onprogress: any = null

    readAsText() {
      this.readyState = 2
      this.result = 'mocked file content'
      if (this.onload) this.onload({ target: this })
    }

    readAsDataURL() {
      this.readyState = 2
      this.result = 'data:text/plain;base64,bW9ja2VkIGZpbGUgY29udGVudA=='
      if (this.onload) this.onload({ target: this })
    }

    abort() {
      this.readyState = 2
      if (this.onabort) this.onabort({ target: this })
    }

    addEventListener() {}
    removeEventListener() {}
    dispatchEvent() { return true }

    static readonly EMPTY = 0
    static readonly LOADING = 1
    static readonly DONE = 2
  } as any

  // Mock Clipboard API
  Object.defineProperty(navigator, 'clipboard', {
    value: {
      writeText: vi.fn().mockResolvedValue(undefined),
      readText: vi.fn().mockResolvedValue(''),
    },
    writable: true,
  })

  // Mock Notification API
  global.Notification = vi.fn().mockImplementation(() => ({
    close: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
  })) as any
  Object.defineProperty(Notification, 'permission', {
    value: 'granted',
    writable: true,
  })
  Object.defineProperty(Notification, 'requestPermission', {
    value: vi.fn().mockResolvedValue('granted'),
    writable: true,
  })
})

// 全局测试清理
afterAll(() => {
  vi.restoreAllMocks()
})

// 自定义测试工具函数
export const createMockEvent = (type: string, properties: any = {}) => {
  const event = new Event(type, { bubbles: true, cancelable: true })
  Object.assign(event, properties)
  return event
}

export const createMockFile = (name: string, content: string, type: string = 'text/plain') => {
  return new File([content], name, { type })
}

export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0))

export const flushPromises = () => new Promise(resolve => setImmediate(resolve))

// 导出常用的测试工具
export { vi, expect, cleanup }

// 全局类型声明
declare global {
  interface Window {
    ResizeObserver: any
    IntersectionObserver: any
  }
}
