"""
认证服务单元测试
测试用户认证、授权、密码处理等功能
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

from app.services.auth_service import AuthService
from app.core.config import settings
from tests.utils.test_helpers import TestDataFactory, MockHelper


class TestAuthService:
    """认证服务测试类"""
    
    def setup_method(self):
        """测试方法设置"""
        self.auth_service = AuthService()
    
    @pytest.mark.unit
    def test_hash_password(self):
        """测试密码哈希"""
        password = "testpassword123"
        hashed = self.auth_service.hash_password(password)
        
        assert hashed != password
        assert len(hashed) > 0
        assert hashed.startswith("$2b$")
    
    @pytest.mark.unit
    def test_verify_password_correct(self):
        """测试正确密码验证"""
        password = "testpassword123"
        hashed = self.auth_service.hash_password(password)
        
        result = self.auth_service.verify_password(password, hashed)
        assert result is True
    
    @pytest.mark.unit
    def test_verify_password_incorrect(self):
        """测试错误密码验证"""
        password = "testpassword123"
        wrong_password = "wrongpassword"
        hashed = self.auth_service.hash_password(password)
        
        result = self.auth_service.verify_password(wrong_password, hashed)
        assert result is False
    
    @pytest.mark.unit
    def test_create_access_token(self):
        """测试访问令牌创建"""
        data = {"sub": "testuser"}
        token = self.auth_service.create_access_token(data)
        
        assert isinstance(token, str)
        assert len(token) > 0
        assert "." in token  # JWT格式包含点分隔符
    
    @pytest.mark.unit
    def test_create_access_token_with_expiry(self):
        """测试带过期时间的访问令牌创建"""
        data = {"sub": "testuser"}
        expires_delta = timedelta(minutes=30)
        token = self.auth_service.create_access_token(data, expires_delta)
        
        assert isinstance(token, str)
        assert len(token) > 0
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_verify_token_valid(self):
        """测试有效令牌验证"""
        data = {"sub": "testuser"}
        token = self.auth_service.create_access_token(data)
        
        payload = await self.auth_service.verify_token(token)
        
        assert payload is not None
        assert payload["sub"] == "testuser"
        assert "exp" in payload
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_verify_token_invalid(self):
        """测试无效令牌验证"""
        invalid_token = "invalid.token.here"
        
        with pytest.raises(Exception):
            await self.auth_service.verify_token(invalid_token)
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_verify_token_expired(self):
        """测试过期令牌验证"""
        data = {"sub": "testuser"}
        # 创建已过期的令牌
        expires_delta = timedelta(seconds=-1)
        token = self.auth_service.create_access_token(data, expires_delta)
        
        with pytest.raises(Exception):
            await self.auth_service.verify_token(token)
    
    @pytest.mark.unit
    def test_generate_refresh_token(self):
        """测试刷新令牌生成"""
        user_id = "test_user_id"
        refresh_token = self.auth_service.generate_refresh_token(user_id)
        
        assert isinstance(refresh_token, str)
        assert len(refresh_token) > 0
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_validate_refresh_token_valid(self):
        """测试有效刷新令牌验证"""
        user_id = "test_user_id"
        refresh_token = self.auth_service.generate_refresh_token(user_id)
        
        # Mock Redis操作
        with patch.object(self.auth_service, 'redis_client') as mock_redis:
            mock_redis.get = MockHelper.create_async_mock(return_value=user_id.encode())
            
            result = await self.auth_service.validate_refresh_token(refresh_token)
            assert result == user_id
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_validate_refresh_token_invalid(self):
        """测试无效刷新令牌验证"""
        invalid_token = "invalid_refresh_token"
        
        # Mock Redis操作
        with patch.object(self.auth_service, 'redis_client') as mock_redis:
            mock_redis.get = MockHelper.create_async_mock(return_value=None)
            
            result = await self.auth_service.validate_refresh_token(invalid_token)
            assert result is None
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_revoke_refresh_token(self):
        """测试撤销刷新令牌"""
        refresh_token = "test_refresh_token"
        
        # Mock Redis操作
        with patch.object(self.auth_service, 'redis_client') as mock_redis:
            mock_redis.delete = MockHelper.create_async_mock(return_value=True)
            
            result = await self.auth_service.revoke_refresh_token(refresh_token)
            assert result is True
            mock_redis.delete.assert_called_once_with(f"refresh_token:{refresh_token}")
    
    @pytest.mark.unit
    def test_generate_password_reset_token(self):
        """测试密码重置令牌生成"""
        email = "<EMAIL>"
        token = self.auth_service.generate_password_reset_token(email)
        
        assert isinstance(token, str)
        assert len(token) > 0
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_verify_password_reset_token_valid(self):
        """测试有效密码重置令牌验证"""
        email = "<EMAIL>"
        token = self.auth_service.generate_password_reset_token(email)
        
        result = await self.auth_service.verify_password_reset_token(token)
        assert result == email
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_verify_password_reset_token_invalid(self):
        """测试无效密码重置令牌验证"""
        invalid_token = "invalid.reset.token"
        
        with pytest.raises(Exception):
            await self.auth_service.verify_password_reset_token(invalid_token)
    
    @pytest.mark.unit
    def test_extract_token_from_header(self):
        """测试从请求头提取令牌"""
        # 测试正确格式
        auth_header = "Bearer test_token_123"
        token = self.auth_service.extract_token_from_header(auth_header)
        assert token == "test_token_123"
        
        # 测试错误格式
        invalid_header = "Invalid header format"
        token = self.auth_service.extract_token_from_header(invalid_header)
        assert token is None
        
        # 测试空值
        token = self.auth_service.extract_token_from_header(None)
        assert token is None
    
    @pytest.mark.unit
    def test_check_password_strength(self):
        """测试密码强度检查"""
        # 强密码
        strong_password = "StrongPassword123!"
        result = self.auth_service.check_password_strength(strong_password)
        assert result["is_strong"] is True
        
        # 弱密码
        weak_password = "123"
        result = self.auth_service.check_password_strength(weak_password)
        assert result["is_strong"] is False
        assert len(result["issues"]) > 0
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_rate_limit_check(self):
        """测试速率限制检查"""
        identifier = "test_user"
        action = "login"
        
        # Mock Redis操作
        with patch.object(self.auth_service, 'redis_client') as mock_redis:
            mock_redis.get = MockHelper.create_async_mock(return_value=b"1")
            mock_redis.incr = MockHelper.create_async_mock(return_value=2)
            mock_redis.expire = MockHelper.create_async_mock(return_value=True)
            
            result = await self.auth_service.check_rate_limit(identifier, action)
            assert isinstance(result, dict)
            assert "allowed" in result
            assert "remaining" in result
