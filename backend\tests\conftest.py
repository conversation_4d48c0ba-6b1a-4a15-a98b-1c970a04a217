"""
数懒平台后端测试配置
pytest共享夹具和配置
"""

import asyncio
import os
import pytest
import pytest_asyncio
from typing import AsyncGenerator, Generator
from unittest.mock import AsyncMock, MagicMock

from fastapi import FastAPI
from fastapi.testclient import TestClient
from httpx import AsyncClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.config import settings
from app.core.database import Base, get_db
from app.models.user import User
from app.models.task import Task
from app.services.auth_service import AuthService
from app.utils.redis_client import RedisClient


# 测试数据库配置
TEST_DATABASE_URL = "sqlite:///./test.db"

# 创建测试数据库引擎
test_engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于异步测试"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def db_session():
    """创建测试数据库会话"""
    # 创建所有表
    Base.metadata.create_all(bind=test_engine)
    
    # 创建会话
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # 清理所有表
        Base.metadata.drop_all(bind=test_engine)


@pytest.fixture(scope="function")
def test_app(db_session) -> FastAPI:
    """创建测试应用实例"""
    # 覆盖数据库依赖
    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    yield app
    
    # 清理依赖覆盖
    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def client(test_app: FastAPI) -> Generator[TestClient, None, None]:
    """创建测试客户端"""
    with TestClient(test_app) as test_client:
        yield test_client


@pytest_asyncio.fixture(scope="function")
async def async_client(test_app: FastAPI) -> AsyncGenerator[AsyncClient, None]:
    """创建异步测试客户端"""
    async with AsyncClient(app=test_app, base_url="http://test") as ac:
        yield ac


@pytest.fixture(scope="function")
def mock_redis():
    """Mock Redis客户端"""
    mock_redis = MagicMock()
    mock_redis.get = AsyncMock(return_value=None)
    mock_redis.set = AsyncMock(return_value=True)
    mock_redis.delete = AsyncMock(return_value=True)
    mock_redis.exists = AsyncMock(return_value=False)
    mock_redis.expire = AsyncMock(return_value=True)
    mock_redis.hget = AsyncMock(return_value=None)
    mock_redis.hset = AsyncMock(return_value=True)
    mock_redis.hdel = AsyncMock(return_value=True)
    mock_redis.lpush = AsyncMock(return_value=1)
    mock_redis.rpop = AsyncMock(return_value=None)
    mock_redis.llen = AsyncMock(return_value=0)
    
    return mock_redis


@pytest.fixture(scope="function")
def mock_auth_service():
    """Mock认证服务"""
    mock_service = MagicMock(spec=AuthService)
    mock_service.create_access_token = MagicMock(return_value="test_token")
    mock_service.verify_token = AsyncMock(return_value={"sub": "test_user"})
    mock_service.hash_password = MagicMock(return_value="hashed_password")
    mock_service.verify_password = MagicMock(return_value=True)
    
    return mock_service


@pytest.fixture(scope="function")
def test_user_data():
    """测试用户数据"""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "testpassword123",
        "full_name": "Test User",
        "is_active": True,
    }


@pytest.fixture(scope="function")
def test_task_data():
    """测试任务数据"""
    return {
        "name": "测试任务",
        "description": "这是一个测试任务",
        "config": {
            "url": "https://example.com",
            "selector": ".test-selector",
            "action": "click",
        },
        "status": "pending",
        "priority": 1,
    }


@pytest.fixture(scope="function")
def authenticated_headers():
    """认证请求头"""
    return {
        "Authorization": "Bearer test_token",
        "Content-Type": "application/json",
    }


@pytest.fixture(scope="function")
def test_user(db_session, test_user_data):
    """创建测试用户"""
    user = User(**test_user_data)
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture(scope="function")
def test_task(db_session, test_user, test_task_data):
    """创建测试任务"""
    task_data = test_task_data.copy()
    task_data["user_id"] = test_user.id
    task = Task(**task_data)
    db_session.add(task)
    db_session.commit()
    db_session.refresh(task)
    return task


# 测试环境配置
@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """设置测试环境"""
    # 设置测试环境变量
    os.environ["TESTING"] = "true"
    os.environ["DATABASE_URL"] = TEST_DATABASE_URL
    os.environ["SECRET_KEY"] = "test_secret_key"
    
    yield
    
    # 清理测试环境
    if "TESTING" in os.environ:
        del os.environ["TESTING"]


# 测试数据清理
@pytest.fixture(autouse=True)
def cleanup_test_data():
    """自动清理测试数据"""
    yield
    # 测试后清理逻辑可以在这里添加


# 性能测试夹具
@pytest.fixture(scope="function")
def benchmark_config():
    """性能测试配置"""
    return {
        "min_rounds": 5,
        "max_time": 1.0,
        "warmup": True,
        "warmup_iterations": 1,
    }


# Mock外部服务
@pytest.fixture(scope="function")
def mock_external_services():
    """Mock外部服务"""
    mocks = {
        "openai": MagicMock(),
        "redis": MagicMock(),
        "s3": MagicMock(),
        "email": MagicMock(),
    }
    
    # 配置Mock行为
    mocks["openai"].chat.completions.create = AsyncMock(
        return_value=MagicMock(choices=[MagicMock(message=MagicMock(content="Mock AI response"))])
    )
    
    return mocks


# 测试标记配置
def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "unit: 单元测试标记"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试标记"
    )
    config.addinivalue_line(
        "markers", "api: API测试标记"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试标记"
    )


# 测试收集配置
def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为没有标记的测试添加默认标记
    for item in items:
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)
