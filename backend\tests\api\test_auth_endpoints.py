"""
认证API端点测试
测试用户注册、登录、令牌刷新等API接口
"""

import pytest
from fastapi import status
from httpx import AsyncClient

from tests.utils.test_helpers import TestDataFactory, APITestHelper, DatabaseTestHelper


class TestAuthEndpoints:
    """认证API端点测试类"""
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_register_user_success(self, async_client: AsyncClient, db_session):
        """测试用户注册成功"""
        user_data = TestDataFactory.create_user_data()
        
        response = await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/register",
            user_data
        )
        
        APITestHelper.assert_response_success(response, status.HTTP_201_CREATED)
        
        data = response.json()
        APITestHelper.assert_json_response(response, ["id", "username", "email", "created_at"])
        
        assert data["username"] == user_data["username"]
        assert data["email"] == user_data["email"]
        assert "password" not in data  # 确保密码不在响应中
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_register_user_duplicate_username(self, async_client: AsyncClient, db_session):
        """测试重复用户名注册"""
        # 创建第一个用户
        user_data = TestDataFactory.create_user_data()
        await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/register",
            user_data
        )
        
        # 尝试使用相同用户名注册
        duplicate_data = TestDataFactory.create_user_data(
            username=user_data["username"],
            email="<EMAIL>"
        )
        
        response = await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/register",
            duplicate_data
        )
        
        APITestHelper.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
        
        data = response.json()
        assert "detail" in data
        assert "username" in data["detail"].lower()
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_register_user_duplicate_email(self, async_client: AsyncClient, db_session):
        """测试重复邮箱注册"""
        # 创建第一个用户
        user_data = TestDataFactory.create_user_data()
        await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/register",
            user_data
        )
        
        # 尝试使用相同邮箱注册
        duplicate_data = TestDataFactory.create_user_data(
            username="different_user",
            email=user_data["email"]
        )
        
        response = await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/register",
            duplicate_data
        )
        
        APITestHelper.assert_response_error(response, status.HTTP_400_BAD_REQUEST)
        
        data = response.json()
        assert "detail" in data
        assert "email" in data["detail"].lower()
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_register_user_invalid_data(self, async_client: AsyncClient):
        """测试无效数据注册"""
        invalid_data = {
            "username": "",  # 空用户名
            "email": "invalid-email",  # 无效邮箱
            "password": "123",  # 弱密码
        }
        
        response = await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/register",
            invalid_data
        )
        
        APITestHelper.assert_response_error(response, status.HTTP_422_UNPROCESSABLE_ENTITY)
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_login_success(self, async_client: AsyncClient, db_session):
        """测试登录成功"""
        # 先注册用户
        user_data = TestDataFactory.create_user_data()
        await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/register",
            user_data
        )
        
        # 登录
        login_data = {
            "username": user_data["username"],
            "password": user_data["password"]
        }
        
        response = await async_client.post(
            "/api/v1/auth/login",
            data=login_data  # 使用form data
        )
        
        APITestHelper.assert_response_success(response)
        
        data = response.json()
        APITestHelper.assert_json_response(response, ["access_token", "refresh_token", "token_type"])
        
        assert data["token_type"] == "bearer"
        assert len(data["access_token"]) > 0
        assert len(data["refresh_token"]) > 0
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_login_invalid_credentials(self, async_client: AsyncClient, db_session):
        """测试无效凭据登录"""
        # 先注册用户
        user_data = TestDataFactory.create_user_data()
        await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/register",
            user_data
        )
        
        # 使用错误密码登录
        login_data = {
            "username": user_data["username"],
            "password": "wrong_password"
        }
        
        response = await async_client.post(
            "/api/v1/auth/login",
            data=login_data
        )
        
        APITestHelper.assert_response_error(response, status.HTTP_401_UNAUTHORIZED)
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_login_nonexistent_user(self, async_client: AsyncClient):
        """测试不存在用户登录"""
        login_data = {
            "username": "nonexistent_user",
            "password": "any_password"
        }
        
        response = await async_client.post(
            "/api/v1/auth/login",
            data=login_data
        )
        
        APITestHelper.assert_response_error(response, status.HTTP_401_UNAUTHORIZED)
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_refresh_token_success(self, async_client: AsyncClient, db_session):
        """测试令牌刷新成功"""
        # 先注册并登录用户
        user_data = TestDataFactory.create_user_data()
        await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/register",
            user_data
        )
        
        login_response = await async_client.post(
            "/api/v1/auth/login",
            data={"username": user_data["username"], "password": user_data["password"]}
        )
        
        login_data = login_response.json()
        refresh_token = login_data["refresh_token"]
        
        # 刷新令牌
        refresh_data = {"refresh_token": refresh_token}
        response = await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/refresh",
            refresh_data
        )
        
        APITestHelper.assert_response_success(response)
        
        data = response.json()
        APITestHelper.assert_json_response(response, ["access_token", "token_type"])
        
        assert data["token_type"] == "bearer"
        assert len(data["access_token"]) > 0
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_refresh_token_invalid(self, async_client: AsyncClient):
        """测试无效刷新令牌"""
        refresh_data = {"refresh_token": "invalid_refresh_token"}
        
        response = await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/refresh",
            refresh_data
        )
        
        APITestHelper.assert_response_error(response, status.HTTP_401_UNAUTHORIZED)
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_logout_success(self, async_client: AsyncClient, db_session):
        """测试登出成功"""
        # 先注册并登录用户
        user_data = TestDataFactory.create_user_data()
        await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/register",
            user_data
        )
        
        login_response = await async_client.post(
            "/api/v1/auth/login",
            data={"username": user_data["username"], "password": user_data["password"]}
        )
        
        login_data = login_response.json()
        access_token = login_data["access_token"]
        refresh_token = login_data["refresh_token"]
        
        # 登出
        headers = TestDataFactory.create_auth_headers(access_token)
        logout_data = {"refresh_token": refresh_token}
        
        response = await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/logout",
            logout_data,
            headers
        )
        
        APITestHelper.assert_response_success(response)
        
        data = response.json()
        assert data["message"] == "Successfully logged out"
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_get_current_user(self, async_client: AsyncClient, db_session):
        """测试获取当前用户信息"""
        # 先注册并登录用户
        user_data = TestDataFactory.create_user_data()
        await APITestHelper.post_json(
            async_client,
            "/api/v1/auth/register",
            user_data
        )
        
        login_response = await async_client.post(
            "/api/v1/auth/login",
            data={"username": user_data["username"], "password": user_data["password"]}
        )
        
        login_data = login_response.json()
        access_token = login_data["access_token"]
        
        # 获取当前用户信息
        response = await APITestHelper.get_with_auth(
            async_client,
            "/api/v1/auth/me",
            access_token
        )
        
        APITestHelper.assert_response_success(response)
        
        data = response.json()
        APITestHelper.assert_json_response(response, ["id", "username", "email", "full_name"])
        
        assert data["username"] == user_data["username"]
        assert data["email"] == user_data["email"]
        assert "password" not in data
    
    @pytest.mark.api
    @pytest.mark.asyncio
    async def test_get_current_user_unauthorized(self, async_client: AsyncClient):
        """测试未授权获取用户信息"""
        response = await async_client.get("/api/v1/auth/me")
        
        APITestHelper.assert_response_error(response, status.HTTP_401_UNAUTHORIZED)
