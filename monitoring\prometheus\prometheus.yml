# Prometheus 配置文件
# 数懒平台监控指标收集配置

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'digital-lazy'
    environment: 'development'

# 告警规则文件
rule_files:
  - "rules/*.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 指标收集配置
scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # 数懒平台后端服务
  - job_name: 'digital-lazy-backend'
    static_configs:
      - targets: ['host.docker.internal:8000']
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # 数懒平台前端服务（如果有指标端点）
  - job_name: 'digital-lazy-frontend'
    static_configs:
      - targets: ['host.docker.internal:3000']
    scrape_interval: 30s
    metrics_path: /api/metrics
    scrape_timeout: 10s

  # Node Exporter - 系统指标
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        regex: '([^:]+)(:[0-9]+)?'
        replacement: '${1}'

  # cAdvisor - 容器指标
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
    metrics_path: /metrics

  # Redis 指标
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s

  # PostgreSQL 指标
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 15s

  # Blackbox Exporter - 端点监控
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://host.docker.internal:8000/health
        - http://host.docker.internal:3000
        - http://host.docker.internal:8000/docs
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # Grafana 指标
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 30s
    metrics_path: /metrics

  # AlertManager 指标
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 30s

  # Jaeger 指标
  - job_name: 'jaeger'
    static_configs:
      - targets: ['jaeger:14269']
    scrape_interval: 30s
    metrics_path: /metrics

  # 自定义应用指标
  - job_name: 'digital-lazy-custom-metrics'
    static_configs:
      - targets: ['host.docker.internal:8000']
    scrape_interval: 10s
    metrics_path: /api/v1/metrics
    scrape_timeout: 5s
    honor_labels: true

# 远程写入配置（可选）
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"

# 远程读取配置（可选）
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"
