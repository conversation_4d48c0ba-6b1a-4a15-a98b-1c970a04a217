import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  plugins: [vue()],
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@/core': resolve(__dirname, 'src/core'),
      '@/services': resolve(__dirname, 'src/services'),
      '@/shared': resolve(__dirname, 'src/shared'),
      '@/types': resolve(__dirname, 'src/types'),
    },
  },

  test: {
    globals: true,
    environment: 'jsdom',

    // 测试文件匹配模式
    include: [
      'tests/**/*.{test,spec}.{js,ts}',
      'src/**/*.{test,spec}.{js,ts}'
    ],

    // 排除文件
    exclude: [
      'node_modules',
      'dist',
      'build',
      'src/types/**/*.d.ts',
      'src/**/*.stories.{js,ts}',
      'tests/e2e/**'
    ],

    // 测试设置文件
    setupFiles: [
      'tests/setup/global-setup.ts',
      'tests/setup/chrome-api-setup.ts',
      'tests/setup/extension-mocks.ts'
    ],

    // 代码覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov', 'clover'],
      reportsDirectory: 'tests/coverage',
      include: ['src/**/*.{js,ts}'],
      exclude: [
        'src/**/*.{test,spec}.{js,ts}',
        'src/types/**',
        'src/**/*.d.ts',
        'src/manifest.ts',
        'src/content-script.ts',
        'src/background.ts'
      ],
      thresholds: {
        global: {
          branches: 85,
          functions: 85,
          lines: 85,
          statements: 85
        },
        // 核心模块要求更高覆盖率
        'src/core/**': {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90
        },
        // 服务模块要求更高覆盖率
        'src/services/**': {
          branches: 88,
          functions: 88,
          lines: 88,
          statements: 88
        }
      }
    },
    
    // 并行测试配置
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false
      }
    },
    
    // 测试超时配置
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // 监听模式配置
    watch: {
      ignore: ['dist/**', 'node_modules/**']
    },
    
    // 测试报告配置
    reporter: ['verbose'],
    
    // Mock 配置
    mockReset: true,
    restoreMocks: true,
    clearMocks: true,
    
    // 环境变量
    env: {
      NODE_ENV: 'test',
      VITEST: 'true'
    }
  }
}); 