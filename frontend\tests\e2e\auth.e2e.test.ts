/**
 * 用户认证 E2E 测试
 * 测试用户注册、登录、登出等完整流程
 */

import { test, expect, Page } from '@playwright/test'

// 测试数据
const testUser = {
  username: 'e2e_test_user',
  email: '<EMAIL>',
  password: 'test123456',
  fullName: 'E2E Test User'
}

// 页面对象模式
class AuthPage {
  constructor(private page: Page) {}

  // 导航到登录页面
  async goto() {
    await this.page.goto('/login')
    await this.page.waitForLoadState('networkidle')
  }

  // 导航到注册页面
  async gotoRegister() {
    await this.page.goto('/register')
    await this.page.waitForLoadState('networkidle')
  }

  // 填写登录表单
  async fillLoginForm(username: string, password: string) {
    await this.page.fill('[data-testid="username-input"]', username)
    await this.page.fill('[data-testid="password-input"]', password)
  }

  // 填写注册表单
  async fillRegisterForm(user: typeof testUser) {
    await this.page.fill('[data-testid="username-input"]', user.username)
    await this.page.fill('[data-testid="email-input"]', user.email)
    await this.page.fill('[data-testid="password-input"]', user.password)
    await this.page.fill('[data-testid="confirm-password-input"]', user.password)
    await this.page.fill('[data-testid="fullname-input"]', user.fullName)
  }

  // 提交登录表单
  async submitLogin() {
    await this.page.click('[data-testid="login-button"]')
  }

  // 提交注册表单
  async submitRegister() {
    await this.page.click('[data-testid="register-button"]')
  }

  // 登出
  async logout() {
    await this.page.click('[data-testid="user-menu"]')
    await this.page.click('[data-testid="logout-button"]')
  }

  // 检查是否已登录
  async isLoggedIn() {
    return await this.page.isVisible('[data-testid="user-menu"]')
  }

  // 检查是否在登录页面
  async isOnLoginPage() {
    return await this.page.isVisible('[data-testid="login-form"]')
  }

  // 获取错误消息
  async getErrorMessage() {
    const errorElement = this.page.locator('[data-testid="error-message"]')
    if (await errorElement.isVisible()) {
      return await errorElement.textContent()
    }
    return null
  }

  // 获取成功消息
  async getSuccessMessage() {
    const successElement = this.page.locator('[data-testid="success-message"]')
    if (await successElement.isVisible()) {
      return await successElement.textContent()
    }
    return null
  }
}

test.describe('用户认证流程', () => {
  let authPage: AuthPage

  test.beforeEach(async ({ page }) => {
    authPage = new AuthPage(page)
  })

  test('用户注册流程', async ({ page }) => {
    // 导航到注册页面
    await authPage.gotoRegister()

    // 验证页面标题
    await expect(page).toHaveTitle(/注册/)

    // 填写注册表单
    await authPage.fillRegisterForm(testUser)

    // 提交表单
    await authPage.submitRegister()

    // 等待注册成功
    await page.waitForURL('/login', { timeout: 10000 })

    // 验证成功消息
    const successMessage = await authPage.getSuccessMessage()
    expect(successMessage).toContain('注册成功')
  })

  test('用户登录流程', async ({ page }) => {
    // 导航到登录页面
    await authPage.goto()

    // 验证页面元素
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible()
    await expect(page.locator('[data-testid="username-input"]')).toBeVisible()
    await expect(page.locator('[data-testid="password-input"]')).toBeVisible()

    // 填写登录表单
    await authPage.fillLoginForm(testUser.username, testUser.password)

    // 提交登录
    await authPage.submitLogin()

    // 等待跳转到首页
    await page.waitForURL('/', { timeout: 10000 })

    // 验证登录成功
    expect(await authPage.isLoggedIn()).toBe(true)

    // 验证用户菜单显示正确的用户名
    await page.click('[data-testid="user-menu"]')
    await expect(page.locator('[data-testid="user-name"]')).toHaveText(testUser.fullName)
  })

  test('登录失败 - 错误的用户名', async ({ page }) => {
    await authPage.goto()

    // 使用错误的用户名
    await authPage.fillLoginForm('wrong_username', testUser.password)
    await authPage.submitLogin()

    // 验证错误消息
    const errorMessage = await authPage.getErrorMessage()
    expect(errorMessage).toContain('用户名或密码错误')

    // 验证仍在登录页面
    expect(await authPage.isOnLoginPage()).toBe(true)
  })

  test('登录失败 - 错误的密码', async ({ page }) => {
    await authPage.goto()

    // 使用错误的密码
    await authPage.fillLoginForm(testUser.username, 'wrong_password')
    await authPage.submitLogin()

    // 验证错误消息
    const errorMessage = await authPage.getErrorMessage()
    expect(errorMessage).toContain('用户名或密码错误')

    // 验证仍在登录页面
    expect(await authPage.isOnLoginPage()).toBe(true)
  })

  test('表单验证', async ({ page }) => {
    await authPage.goto()

    // 尝试提交空表单
    await authPage.submitLogin()

    // 验证表单验证消息
    await expect(page.locator('[data-testid="username-error"]')).toBeVisible()
    await expect(page.locator('[data-testid="password-error"]')).toBeVisible()

    // 验证仍在登录页面
    expect(await authPage.isOnLoginPage()).toBe(true)
  })

  test('用户登出流程', async ({ page }) => {
    // 先登录
    await authPage.goto()
    await authPage.fillLoginForm(testUser.username, testUser.password)
    await authPage.submitLogin()
    await page.waitForURL('/')

    // 验证已登录
    expect(await authPage.isLoggedIn()).toBe(true)

    // 执行登出
    await authPage.logout()

    // 等待跳转到登录页面
    await page.waitForURL('/login', { timeout: 10000 })

    // 验证已登出
    expect(await authPage.isLoggedIn()).toBe(false)
    expect(await authPage.isOnLoginPage()).toBe(true)
  })

  test('记住登录状态', async ({ page, context }) => {
    // 登录
    await authPage.goto()
    await authPage.fillLoginForm(testUser.username, testUser.password)
    
    // 勾选"记住我"选项
    await page.check('[data-testid="remember-me-checkbox"]')
    
    await authPage.submitLogin()
    await page.waitForURL('/')

    // 关闭页面并重新打开
    await page.close()
    const newPage = await context.newPage()
    const newAuthPage = new AuthPage(newPage)

    // 访问首页，应该仍然保持登录状态
    await newPage.goto('/')
    expect(await newAuthPage.isLoggedIn()).toBe(true)
  })

  test('密码可见性切换', async ({ page }) => {
    await authPage.goto()

    const passwordInput = page.locator('[data-testid="password-input"]')
    const toggleButton = page.locator('[data-testid="password-toggle"]')

    // 初始状态应该是密码类型
    await expect(passwordInput).toHaveAttribute('type', 'password')

    // 点击切换按钮
    await toggleButton.click()

    // 应该变为文本类型
    await expect(passwordInput).toHaveAttribute('type', 'text')

    // 再次点击切换回密码类型
    await toggleButton.click()
    await expect(passwordInput).toHaveAttribute('type', 'password')
  })

  test('响应式设计 - 移动端', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 })

    await authPage.goto()

    // 验证移动端布局
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible()
    
    // 验证表单在移动端的显示
    const form = page.locator('[data-testid="login-form"]')
    const boundingBox = await form.boundingBox()
    
    // 表单应该适应移动端宽度
    expect(boundingBox?.width).toBeLessThanOrEqual(375)
  })

  test('键盘导航', async ({ page }) => {
    await authPage.goto()

    // 使用 Tab 键导航
    await page.keyboard.press('Tab') // 用户名输入框
    await expect(page.locator('[data-testid="username-input"]')).toBeFocused()

    await page.keyboard.press('Tab') // 密码输入框
    await expect(page.locator('[data-testid="password-input"]')).toBeFocused()

    await page.keyboard.press('Tab') // 记住我复选框
    await expect(page.locator('[data-testid="remember-me-checkbox"]')).toBeFocused()

    await page.keyboard.press('Tab') // 登录按钮
    await expect(page.locator('[data-testid="login-button"]')).toBeFocused()

    // 使用 Enter 键提交表单
    await page.fill('[data-testid="username-input"]', testUser.username)
    await page.fill('[data-testid="password-input"]', testUser.password)
    await page.keyboard.press('Enter')

    // 应该尝试提交表单
    await page.waitForURL('/', { timeout: 10000 })
  })
})
