import { defineConfig, devices } from '@playwright/test'

/**
 * Playwright E2E 测试配置
 * 配置端到端测试环境和浏览器
 */
export default defineConfig({
  // 测试目录
  testDir: './tests/e2e',
  
  // 测试文件匹配模式
  testMatch: '**/*.e2e.{test,spec}.{js,ts}',
  
  // 全局设置
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  
  // 报告配置
  reporter: [
    ['html', { outputFolder: 'tests/reports/playwright-report' }],
    ['json', { outputFile: 'tests/reports/test-results.json' }],
    ['junit', { outputFile: 'tests/reports/junit.xml' }],
    ['line']
  ],
  
  // 全局测试配置
  use: {
    // 基础URL
    baseURL: 'http://localhost:3000',
    
    // 浏览器配置
    headless: !!process.env.CI,
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    
    // 截图和视频
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',
    
    // 超时配置
    actionTimeout: 10000,
    navigationTimeout: 30000,
    
    // 语言设置
    locale: 'zh-CN',
    timezoneId: 'Asia/Shanghai',
    
    // 用户代理
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  },

  // 项目配置 - 不同浏览器和设备
  projects: [
    // 桌面浏览器
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    
    // 移动设备
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
    
    // 平板设备
    {
      name: 'Tablet',
      use: { ...devices['iPad Pro'] },
    },
    
    // 高分辨率桌面
    {
      name: 'Desktop Large',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 },
      },
    },
    
    // 低分辨率桌面
    {
      name: 'Desktop Small',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1024, height: 768 },
      },
    },
  ],

  // 输出目录
  outputDir: 'tests/results',
  
  // 全局设置和清理
  globalSetup: require.resolve('./tests/e2e/global-setup.ts'),
  globalTeardown: require.resolve('./tests/e2e/global-teardown.ts'),
  
  // Web服务器配置（用于启动开发服务器）
  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },
  
  // 期望配置
  expect: {
    // 断言超时
    timeout: 5000,
    
    // 截图比较
    threshold: 0.2,
    
    // 动画处理
    toHaveScreenshot: {
      mode: 'css',
      animations: 'disabled',
    },
    
    // 页面截图
    toMatchSnapshot: {
      threshold: 0.2,
    },
  },
  
  // 测试超时
  timeout: 30000,
  
  // 最大失败数
  maxFailures: process.env.CI ? 10 : undefined,
  
  // 元数据
  metadata: {
    platform: process.platform,
    node: process.version,
    ci: !!process.env.CI,
  },
})
