[tool:pytest]
# 数懒平台后端测试配置
# 基于pytest的全面测试框架配置

# 测试发现配置
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 测试执行配置
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=app
    --cov-report=term-missing
    --cov-report=html:tests/coverage/html
    --cov-report=xml:tests/coverage/coverage.xml
    --cov-report=json:tests/coverage/coverage.json
    --cov-fail-under=80
    --cov-branch
    --html=tests/reports/report.html
    --self-contained-html
    --junitxml=tests/reports/junit.xml
    --maxfail=5
    --durations=10

# 并行测试配置
# 使用 pytest-xdist 进行并行测试
# 取消注释下面的行来启用并行测试
# -n auto

# 异步测试配置
asyncio_mode = auto

# 测试标记定义
markers =
    unit: 单元测试 - 测试单个函数或类的功能
    integration: 集成测试 - 测试多个组件的协作
    api: API测试 - 测试HTTP接口
    database: 数据库测试 - 测试数据库操作
    auth: 认证测试 - 测试用户认证和授权
    task: 任务测试 - 测试任务管理功能
    websocket: WebSocket测试 - 测试实时通信
    slow: 慢速测试 - 运行时间较长的测试
    external: 外部依赖测试 - 需要外部服务的测试
    performance: 性能测试 - 性能基准测试
    security: 安全测试 - 安全相关测试
    smoke: 冒烟测试 - 基本功能验证
    regression: 回归测试 - 防止功能退化

# 测试过滤配置
# 默认跳过慢速测试和外部依赖测试
# 使用 pytest -m "not slow" 来跳过慢速测试
# 使用 pytest -m "slow" 来只运行慢速测试

# 警告过滤
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*
    ignore::UserWarning:alembic.*

# 测试超时配置
timeout = 300
timeout_method = thread

# 环境变量配置
env =
    TESTING = true
    DATABASE_URL = postgresql://test:test@localhost:5432/test_digital_lazy
    REDIS_URL = redis://localhost:6379/1
    SECRET_KEY = test_secret_key_for_testing_only
    ALGORITHM = HS256
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
    LOG_LEVEL = DEBUG

# 最小Python版本
minversion = 3.11

# 测试目录结构要求
required_plugins = 
    pytest-asyncio
    pytest-cov
    pytest-mock
    pytest-html
    pytest-xdist

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

log_file = tests/logs/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d %(funcName)s(): %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# 测试数据配置
# 测试数据文件路径
testdata_paths = tests/fixtures tests/data

# 缓存配置
cache_dir = tests/.pytest_cache

# 输出配置
console_output_style = progress
