/**
 * 前端测试工具函数
 * 提供React组件测试的常用工具和包装器
 */

import React, { ReactElement, ReactNode } from 'react'
import { render, RenderOptions, RenderResult } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { vi } from 'vitest'

// 测试主题配置
const testTheme = {
  token: {
    colorPrimary: '#1890ff',
  },
}

// 创建测试用的 QueryClient
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      cacheTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
})

// 测试包装器组件
interface TestWrapperProps {
  children: ReactNode
  queryClient?: QueryClient
  initialRoute?: string
}

const TestWrapper: React.FC<TestWrapperProps> = ({ 
  children, 
  queryClient = createTestQueryClient(),
  initialRoute = '/'
}) => {
  // 设置初始路由
  if (initialRoute !== '/') {
    window.history.pushState({}, 'Test page', initialRoute)
  }

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider locale={zhCN} theme={testTheme}>
          {children}
        </ConfigProvider>
      </QueryClientProvider>
    </BrowserRouter>
  )
}

// 自定义渲染函数
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
  initialRoute?: string
  wrapper?: React.ComponentType<any>
}

const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
): RenderResult => {
  const { queryClient, initialRoute, wrapper, ...renderOptions } = options

  const Wrapper = wrapper || (({ children }) => (
    <TestWrapper queryClient={queryClient} initialRoute={initialRoute}>
      {children}
    </TestWrapper>
  ))

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock 数据生成器
export const mockUser = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  fullName: '测试用户',
  avatar: 'https://example.com/avatar.jpg',
  isActive: true,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
}

export const mockTask = {
  id: '1',
  name: '测试任务',
  description: '这是一个测试任务',
  status: 'pending' as const,
  priority: 1,
  config: {
    url: 'https://example.com',
    selector: '.test-selector',
    action: 'click',
  },
  userId: '1',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
}

// API Mock 工具
export const createMockResponse = <T>(data: T, status = 200) => ({
  ok: status >= 200 && status < 300,
  status,
  json: () => Promise.resolve(data),
  text: () => Promise.resolve(JSON.stringify(data)),
})

export const mockFetch = (response: any, delay = 0) => {
  return vi.fn().mockImplementation(() => 
    new Promise(resolve => 
      setTimeout(() => resolve(createMockResponse(response)), delay)
    )
  )
}

// 表单测试工具
export const fillForm = async (form: HTMLFormElement, data: Record<string, any>) => {
  const { fireEvent } = await import('@testing-library/react')
  
  Object.entries(data).forEach(([name, value]) => {
    const input = form.querySelector(`[name="${name}"]`) as HTMLInputElement
    if (input) {
      fireEvent.change(input, { target: { value } })
    }
  })
}

// 等待工具
export const waitFor = async (callback: () => void | Promise<void>, timeout = 1000) => {
  const { waitFor: rtlWaitFor } = await import('@testing-library/react')
  return rtlWaitFor(callback, { timeout })
}

export const waitForElementToBeRemoved = async (element: HTMLElement) => {
  const { waitForElementToBeRemoved: rtlWaitForElementToBeRemoved } = await import('@testing-library/react')
  return rtlWaitForElementToBeRemoved(element)
}

// 用户交互工具
export const createUserEvent = async () => {
  const userEvent = await import('@testing-library/user-event')
  return userEvent.default.setup()
}

// 路由测试工具
export const mockNavigate = vi.fn()
export const mockLocation = {
  pathname: '/',
  search: '',
  hash: '',
  state: null,
  key: 'default',
}

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => mockLocation,
  }
})

// 存储测试工具
export const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

export const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

// WebSocket Mock
export class MockWebSocket {
  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  readyState = MockWebSocket.CONNECTING
  url: string
  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null

  constructor(url: string) {
    this.url = url
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN
      if (this.onopen) {
        this.onopen(new Event('open'))
      }
    }, 0)
  }

  send(data: string) {
    if (this.readyState === MockWebSocket.OPEN) {
      // 模拟发送成功
      return
    }
    throw new Error('WebSocket is not open')
  }

  close() {
    this.readyState = MockWebSocket.CLOSED
    if (this.onclose) {
      this.onclose(new CloseEvent('close'))
    }
  }

  addEventListener(type: string, listener: EventListener) {
    // 简单实现
  }

  removeEventListener(type: string, listener: EventListener) {
    // 简单实现
  }

  dispatchEvent(event: Event): boolean {
    return true
  }
}

// 性能测试工具
export const measureRenderTime = async (renderFn: () => void) => {
  const start = performance.now()
  renderFn()
  await new Promise(resolve => setTimeout(resolve, 0)) // 等待渲染完成
  const end = performance.now()
  return end - start
}

// 可访问性测试工具
export const checkA11y = async (container: HTMLElement) => {
  // 这里可以集成 @axe-core/react 或其他可访问性测试工具
  // 简单的检查示例
  const issues: string[] = []
  
  // 检查是否有 alt 属性缺失的图片
  const images = container.querySelectorAll('img')
  images.forEach((img, index) => {
    if (!img.getAttribute('alt')) {
      issues.push(`Image at index ${index} is missing alt attribute`)
    }
  })
  
  // 检查是否有缺失 label 的表单控件
  const inputs = container.querySelectorAll('input, select, textarea')
  inputs.forEach((input, index) => {
    const id = input.getAttribute('id')
    const ariaLabel = input.getAttribute('aria-label')
    const ariaLabelledby = input.getAttribute('aria-labelledby')
    
    if (!id && !ariaLabel && !ariaLabelledby) {
      const label = container.querySelector(`label[for="${id}"]`)
      if (!label) {
        issues.push(`Form control at index ${index} is missing label`)
      }
    }
  })
  
  return issues
}

// 快照测试工具
export const createSnapshot = (component: ReactElement, options?: CustomRenderOptions) => {
  const { container } = customRender(component, options)
  return container.firstChild
}

// 重新导出常用的测试工具
export * from '@testing-library/react'
export { customRender as render }
export { TestWrapper }
export { createTestQueryClient }
