[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "digital-lazy-backend"
version = "1.0.0"
description = "数懒平台后端服务 - 基于FastAPI的智能浏览代理平台"
authors = [
    {name = "数懒平台开发团队", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
]
keywords = ["fastapi", "web", "api", "automation", "browser", "crawler"]

[project.urls]
Homepage = "https://github.com/agentic-agi/agent-crawl"
Documentation = "https://github.com/agentic-agi/agent-crawl/docs"
Repository = "https://github.com/agentic-agi/agent-crawl.git"
"Bug Tracker" = "https://github.com/agentic-agi/agent-crawl/issues"

# Black 代码格式化配置
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | \.pytest_cache
  | _build
  | buck-out
  | build
  | dist
  | migrations
)/
'''

# isort 导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app", "tests"]
known_third_party = ["fastapi", "pydantic", "sqlalchemy", "alembic"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# MyPy 类型检查配置
[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true
show_error_context = true
pretty = true

# 忽略第三方库的类型检查
[[tool.mypy.overrides]]
module = [
    "redis.*",
    "celery.*",
    "kombu.*",
    "aio_pika.*",
    "psycopg2.*",
    "asyncpg.*",
    "alembic.*",
    "pytest.*",
    "factory_boy.*",
    "faker.*",
    "httpx.*",
    "aiohttp.*",
    "PIL.*",
    "cv2.*",
    "torch.*",
    "transformers.*",
    "sklearn.*",
    "pytesseract.*",
    "pdf2image.*",
]
ignore_missing_imports = true

# Pytest 配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:tests/coverage/html",
    "--cov-report=xml:tests/coverage/coverage.xml",
    "--cov-fail-under=80",
    "--cov-branch",
    "--html=tests/reports/report.html",
    "--self-contained-html",
    "--junitxml=tests/reports/junit.xml",
    "--maxfail=5",
    "--durations=10",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: 单元测试标记",
    "integration: 集成测试标记",
    "api: API测试标记",
    "database: 数据库测试标记",
    "auth: 认证测试标记",
    "task: 任务测试标记",
    "websocket: WebSocket测试标记",
    "slow: 慢速测试标记",
    "external: 外部依赖测试标记",
    "performance: 性能测试标记",
    "security: 安全测试标记",
    "smoke: 冒烟测试标记",
    "regression: 回归测试标记",
]
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore::UserWarning:sqlalchemy.*",
    "ignore::UserWarning:alembic.*",
]
asyncio_mode = "auto"

# Coverage 配置
[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
    "*/migrations/*",
    "app/main.py",
    "app/simple_main.py",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "tests/coverage/html"

[tool.coverage.xml]
output = "tests/coverage/coverage.xml"

# Bandit 安全检查配置
[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv"]
skips = ["B101", "B601"]  # 跳过 assert 和 shell 注入检查（在测试中常用）

# Flake8 配置（在 setup.cfg 中配置，因为 flake8 不支持 pyproject.toml）
# 见 setup.cfg 文件

# Ruff 配置（可选的更快的 linter）
[tool.ruff]
line-length = 88
target-version = "py311"
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",
]

[tool.ruff.mccabe]
max-complexity = 10

[tool.ruff.isort]
known-first-party = ["app", "tests"]

# Pre-commit hooks 配置
[tool.pre-commit]
repos = [
    {
        repo = "https://github.com/pre-commit/pre-commit-hooks",
        rev = "v4.4.0",
        hooks = [
            {id = "trailing-whitespace"},
            {id = "end-of-file-fixer"},
            {id = "check-yaml"},
            {id = "check-added-large-files"},
            {id = "check-merge-conflict"},
            {id = "debug-statements"},
        ]
    },
    {
        repo = "https://github.com/psf/black",
        rev = "23.11.0",
        hooks = [{id = "black"}]
    },
    {
        repo = "https://github.com/pycqa/isort",
        rev = "5.12.0",
        hooks = [{id = "isort"}]
    },
    {
        repo = "https://github.com/pycqa/flake8",
        rev = "6.1.0",
        hooks = [{id = "flake8"}]
    },
    {
        repo = "https://github.com/pre-commit/mirrors-mypy",
        rev = "v1.7.1",
        hooks = [{id = "mypy", additional_dependencies = ["types-all"]}]
    },
]
